apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "fpaas-apigw-http.fullname" . }}-config
  labels:
    {{- include "fpaas-apigw-http.labels" . | nindent 4 }}
data:
  application.yml: |
    spring:
      application:
        name: FPAAS-APIGW-HTTP
      profiles:
        active: {{ .Values.app.profile }}

  application-fat.yml: |
    athena:
      jdbc:
        datasource:
          dialect: mysql
          driverClassName: com.mysql.jdbc.Driver
          enabled: true
          initialSize: 1
          maxActive: 5
          maxIdle: 5
          minIdle: 1
          password: ${MYSQL_PASSWORD}
          url: jdbc:mysql://${DATASOURCE_HOST}/fpaas?serverTimezone=Asia/Shanghai&autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull
          username: {{ .Values.external.mysql.username }}
          validationQuery: 'select 1 '
      redis:
        cluster:
          enabled: false
        url: {{ .Values.external.redis.host }}:{{ .Values.external.redis.port }}
        password: ${REDIS_PASSWORD}
        ssl: false
      rpc:
        ssl:
          client:
            enable: false
      service:
        serializer:
          type: kryo4_ext
    dataSource:
      host: {{ .Values.external.mysql.host }}:{{ .Values.external.mysql.port }}
    galaxy:
      appId: ${spring.application.name}
      dataCenter: dc01
      logicUnitId: ''
      phyUnitId: ''
      profile: {{ .Values.app.profile }}
      tenantId: fpaas
      biz-monitor:
        dir: /tmp/monitor/${spring.application.name}
        enable: false
        max-size: 10
    management:
      endpoint:
        health:
          show-details: ALWAYS
        prometheus:
          enabled: true
      endpoints:
        web:
          exposure:
            exclude: env,beans
            include: '*'
      metrics:
        tags:
          application: ${spring.application.name}
    server:
      servlet:
        context-parameters:
          asyncTimeout: 300
      ssl:
        enabled: false
    spring:
      cloud:
        gateway:
          httpclient:
            ssl:
              use-insecure-trust-manager: false
        nacos:
          discovery:
            server-addr: {{ .Values.external.nacos.host }}:{{ .Values.external.nacos.port }}
            namespace: {{ .Values.external.nacos.namespace }}
            watch:
              enabled: true
          config:
            enabled: false
            file-extension: yaml
            group: DEFAULT_GROUP
            server-addr: {{ .Values.external.nacos.host }}:{{ .Values.external.nacos.port }}
            timeout: 5000
        consul:
          config:
            format: YAML
            prefix: config/cbs
            defaultContext: default
          discovery:
            prefer-ip-address: false
            healthCheckInterval: 15s
            healthCheckCriticalTimeout: 30s
        sentinel:
          enabled: false
          transport:
            dashboard: 'localhost:8088  '
            port: 9990
      servlet:
        multipart:
          enabled: true
          location: /tmp
          max-file-size: 300000000
          max-request-size: 300000000
      kafka:
        bootstrap-servers: {{ .Values.external.kafka.host }}:{{ .Values.external.kafka.port }}
        producer:
          batch-size: 16384
          acks: -1
          retries: 1
          buffer-memory: 33554432
          key-serializer: org.apache.kafka.common.serialization.StringSerializer
          value-serializer: org.apache.kafka.common.serialization.StringSerializer
          properties:
            linger:
              ms: 2000
    tokenRefreshTime: {{ .Values.config.tokenRefreshTime }}
    tokenTimeout: {{ .Values.config.tokenTimeout }}
    logging:
      config: classpath:logback-spring.xml
    http:
      maxSize: {{ .Values.config.httpMaxSize }}
    fpaas:
      audit-log:
        send-type: http
        http:
          app-id:
            - 88888888
          queue-size: 1000
        kafka:
          topic: audit_log
    feign:
      client:
        config:
          default:
            connectionTimeout: 10000
            readTimeout: 4000

  bootstrap.yml: |
    IdentityName: identity
    applications: IAM,FPAAS
    athena:
      authtoken:
        cache: true
        enabled: true
      cache:
        redis:
          enabled: true
      datasource:
        enabled: true
      rpc:
        rule:
          nacos: athena.rule
      svcgw:
        authToken:
          headerName: AthenaAuthToken
        cookie:
          enabled: false
      zipkin:
        enabled: false
      message-source:
        enabled: true
    context:
      name: svcgw
      url: http://localhost:8180
      url.service: ${context.url}/${context.name}/at/
    loginServices: AAS.Login
    logoutServices: AAS.Logout
    server:
      port: {{ .Values.service.targetPort }}
    spring:
      application:
        name: FPAAS-APIGW-HTTP
      cloud:
        gateway:
          enabled: false
        inetutils:
          use-only-site-local-interfaces: true
        nacos:
          discovery:
            enabled: true
          config:
            enabled: true
        consul:
          enabled: false
          config:
            enabled: false
          discovery:
            enabled: false
      sleuth:
        sampler:
          probability: 1.0
      zipkin:
        baseUrl: http://zipkin-host:9411/
        enabled: false
        sender:
          type: web
        service:
          name: ${spring.application.name}
    fpaas:
      kafka:
        topic:
          audit-log: audit_log
