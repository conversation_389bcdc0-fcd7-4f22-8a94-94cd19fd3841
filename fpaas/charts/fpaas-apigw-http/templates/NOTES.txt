1. Get the application URL by running these commands:
{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  {{- range .paths }}
  http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
{{- else if contains "NodePort" .Values.service.type }}
  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "fpaas-apigw-http.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "LoadBalancer" .Values.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{ .Release.Namespace }} svc -w {{ include "fpaas-apigw-http.fullname" . }}'
  export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "fpaas-apigw-http.fullname" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
  echo http://$SERVICE_IP:{{ .Values.service.port }}
{{- else if contains "ClusterIP" .Values.service.type }}
  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "fpaas-apigw-http.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:8080 to use your application"
  kubectl --namespace {{ .Release.Namespace }} port-forward $POD_NAME 8080:$CONTAINER_PORT
{{- end }}

2. Check application health:
  kubectl get pods -l "app.kubernetes.io/name={{ include "fpaas-apigw-http.name" . }},app.kubernetes.io/instance={{ .Release.Name }}"

3. View application logs:
  kubectl logs -f deployment/{{ include "fpaas-apigw-http.fullname" . }}

4. Access health check endpoint:
{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  curl http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}/actuator/health
{{- end }}
{{- else }}
  kubectl port-forward svc/{{ include "fpaas-apigw-http.fullname" . }} 8084:{{ .Values.service.port }}
  curl http://localhost:8084/actuator/health
{{- end }}

5. Monitor application metrics (Prometheus):
{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  curl http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}/actuator/prometheus
{{- end }}
{{- else }}
  kubectl port-forward svc/{{ include "fpaas-apigw-http.fullname" . }} 8084:{{ .Values.service.port }}
  curl http://localhost:8084/actuator/prometheus
{{- end }}

Configuration:
- Profile: {{ .Values.app.profile }}
- MySQL Host: {{ .Values.external.mysql.host }}:{{ .Values.external.mysql.port }}
- Redis Host: {{ .Values.external.redis.host }}:{{ .Values.external.redis.port }}
- Nacos Host: {{ .Values.external.nacos.host }}:{{ .Values.external.nacos.port }}
- Kafka Host: {{ .Values.external.kafka.host }}:{{ .Values.external.kafka.port }}
