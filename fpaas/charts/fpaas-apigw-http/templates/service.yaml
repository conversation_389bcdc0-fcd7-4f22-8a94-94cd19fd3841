apiVersion: v1
kind: Service
metadata:
  name: {{ include "fpaas-apigw-http.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "fpaas-apigw-http.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "fpaas-apigw-http.selectorLabels" . | nindent 4 }}
