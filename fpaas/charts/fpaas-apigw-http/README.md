# FPAAS API Gateway HTTP Helm Chart

This Helm chart deploys the FPAAS API Gateway HTTP service to Kubernetes cluster.

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- MySQL database
- Redis server
- Nacos service registry
- Kafka message queue

## Installation

### 1. Add the chart repository (if applicable)

```bash
helm repo add fpaas-charts https://your-helm-repo.com
helm repo update
```

### 2. Install the chart

```bash
# Install with default values
helm install fpaas-apigw-http ./helm-chart/fpaas-apigw-http

# Install with custom values
helm install fpaas-apigw-http ./helm-chart/fpaas-apigw-http -f custom-values.yaml

# Install in specific namespace
helm install fpaas-apigw-http ./helm-chart/fpaas-apigw-http -n fpaas-system --create-namespace
```

### 3. Upgrade the deployment

```bash
helm upgrade fpaas-apigw-http ./helm-chart/fpaas-apigw-http -f custom-values.yaml
```

### 4. Uninstall the chart

```bash
helm uninstall fpaas-apigw-http
```

## Configuration

The following table lists the configurable parameters and their default values.

### Application Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `app.name` | Application name | `fpaas-apigw-http` |
| `app.version` | Application version | `2.6.1` |
| `app.profile` | Spring profile to use | `fat` |

### Image Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `image.repository` | Image repository | `your-registry/fpaas-apigw-http` |
| `image.tag` | Image tag | `2.6.1` |
| `image.pullPolicy` | Image pull policy | `IfNotPresent` |

### Service Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `service.type` | Service type | `ClusterIP` |
| `service.port` | Service port | `8084` |
| `service.targetPort` | Container port | `8084` |

### Resource Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `resources.limits.cpu` | CPU limit | `2000m` |
| `resources.limits.memory` | Memory limit | `2Gi` |
| `resources.requests.cpu` | CPU request | `500m` |
| `resources.requests.memory` | Memory request | `1Gi` |

### JVM Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `jvm.xms` | Initial heap size | `1024m` |
| `jvm.xmx` | Maximum heap size | `1024m` |
| `jvm.xmn` | Young generation size | `256m` |
| `jvm.metaspaceSize` | Metaspace size | `512m` |
| `jvm.maxMetaspaceSize` | Maximum metaspace size | `1024m` |

### External Dependencies

| Parameter | Description | Default |
|-----------|-------------|---------|
| `external.mysql.host` | MySQL host | `onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com` |
| `external.mysql.port` | MySQL port | `3306` |
| `external.mysql.database` | MySQL database | `fpaas` |
| `external.mysql.username` | MySQL username | `fpaas` |
| `external.redis.host` | Redis host | `redis-host` |
| `external.redis.port` | Redis port | `6379` |
| `external.nacos.host` | Nacos host | `nacos-host` |
| `external.nacos.port` | Nacos port | `8848` |
| `external.nacos.namespace` | Nacos namespace | `hca0m5ezga95_xkf6m5ezgqn9` |
| `external.kafka.host` | Kafka host | `kafka-host` |
| `external.kafka.port` | Kafka port | `8092` |

### Autoscaling

| Parameter | Description | Default |
|-----------|-------------|---------|
| `autoscaling.enabled` | Enable HPA | `false` |
| `autoscaling.minReplicas` | Minimum replicas | `1` |
| `autoscaling.maxReplicas` | Maximum replicas | `10` |
| `autoscaling.targetCPUUtilizationPercentage` | Target CPU utilization | `80` |
| `autoscaling.targetMemoryUtilizationPercentage` | Target memory utilization | `80` |

### Persistence

| Parameter | Description | Default |
|-----------|-------------|---------|
| `persistence.enabled` | Enable persistent storage for logs | `true` |
| `persistence.storageClass` | Storage class | `""` |
| `persistence.accessMode` | Access mode | `ReadWriteOnce` |
| `persistence.size` | Storage size | `10Gi` |
| `persistence.mountPath` | Mount path | `/app/logs` |

## Custom Values Example

Create a `custom-values.yaml` file:

```yaml
# Custom values for production deployment
replicaCount: 3

image:
  repository: your-registry.com/fpaas-apigw-http
  tag: "2.6.1"

resources:
  limits:
    cpu: 4000m
    memory: 4Gi
  requests:
    cpu: 1000m
    memory: 2Gi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 20
  targetCPUUtilizationPercentage: 70

ingress:
  enabled: true
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
  hosts:
    - host: fpaas-apigw.yourdomain.com
      paths:
        - path: /
          pathType: Prefix

external:
  mysql:
    host: "your-mysql-host.com"
    port: 3306
  redis:
    host: "your-redis-host.com"
    port: 6379
  nacos:
    host: "your-nacos-host.com"
    port: 8848
  kafka:
    host: "your-kafka-host.com"
    port: 9092

secrets:
  mysql:
    password: "your-mysql-password"
  redis:
    password: "your-redis-password"
```

## Monitoring

The application exposes the following endpoints for monitoring:

- Health check: `/actuator/health`
- Metrics (Prometheus): `/actuator/prometheus`
- Application info: `/actuator/info`

## Troubleshooting

### Check pod status
```bash
kubectl get pods -l app.kubernetes.io/name=fpaas-apigw-http
```

### View logs
```bash
kubectl logs -f deployment/fpaas-apigw-http
```

### Check configuration
```bash
kubectl describe configmap fpaas-apigw-http-config
kubectl describe secret fpaas-apigw-http-secret
```

### Port forward for local testing
```bash
kubectl port-forward svc/fpaas-apigw-http 8084:8084
```

## Security Considerations

1. **Secrets Management**: Store sensitive information like database passwords in Kubernetes secrets
2. **RBAC**: Configure appropriate service account permissions
3. **Network Policies**: Implement network policies to restrict traffic
4. **Image Security**: Use trusted base images and scan for vulnerabilities
5. **Resource Limits**: Set appropriate resource limits to prevent resource exhaustion

## Support

For issues and questions, please contact the FPAAS team or create an issue in the project repository.
