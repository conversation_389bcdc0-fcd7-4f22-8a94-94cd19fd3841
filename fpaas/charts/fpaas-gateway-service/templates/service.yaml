apiVersion: v1
kind: Service
metadata:
  name: {{ include "fpaas-gateway-service.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "fpaas-gateway-service.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "fpaas-gateway-service.selectorLabels" . | nindent 4 }}
