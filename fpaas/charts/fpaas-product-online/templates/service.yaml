apiVersion: v1
kind: Service
metadata:
  name: {{ include "fpaas-product-online.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "fpaas-product-online.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "fpaas-product-online.selectorLabels" . | nindent 4 }}
