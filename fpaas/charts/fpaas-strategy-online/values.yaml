ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: fpaas-strategy-online.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: fpaas-strategy-online-tls
  #    hosts:
  #      - fpaas-strategy-online.local

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicas: 2
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

image:
  repository: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/fpaas-strategy-online"
  tag: "0.0.1-alpha"
  pullPolicy: "IfNotPresent"

configDirectory: "/data/app/config"
logDirectory: "/logs"

# Service configuration
service:
  type: "ClusterIP"
  port: 8080
  targetPort: 8080

# Resource configuration
resources:
  limits:
    cpu: "500m"
    memory: "512Mi"
  requests:
    cpu: "50m"
    memory: "64Mi"

# Environment variables
env:
  - name: "DB_HOST"
    value: "mysql-service"
  - name: "DB_PORT"
    value: "3306"

# Persistent Volume configuration
persistentVolume:
  enabled: false
  storageClass: "" # 如需指定存储类，填写名称，否则留空
  accessMode: "ReadWriteOnce"
  size: "1Gi"
  mountPath: "/app/data" # 容器内挂载路径

# Additional volumes
volumeMounts:
  - name: "logs"
    mountPath: "{{ .Values.logDirectory | default \"/logs\" }}"

volumes:
  - name: "logs"
    hostPath:
      path: /home/<USER>/logs
      type: DirectoryOrCreate

# Liveness probe
livenessProbe:
  httpGet:
    path: "/health"
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10

# Readiness probe
readinessProbe:
  httpGet:
    path: "/ready"
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5

# ConfigMap configuration
configMap:
  enabled: true
  data:
    application-fat.yml: |
      server:
        #web端口
        port: 8080
      spring:
        cloud:
          nacos:
            discovery:
              server-addr: {{ include "fpaas-strategy-online.fullname" . }}-service.{{ .Values.global.namespace }}.svc.cluster.local:8848
              prefer-ip-address: false  # 禁用IP注册
              use-hostname: true        # 使用K8s Service名称
        datasource:
          username: {{ .Values.global.middleware.defaultDatabase.username }}
          password: {{ .Values.global.middleware.defaultDatabase.password }}
          driver-class-name: oracle.jdbc.OracleDriver
          url: {{ .Values.global.middleware.defaultDatabase.host }}
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            #初始化物理连接个数
            initial-size: 20
            #最小连接池数量
            min-idle: 20
            #最大连接池数量
            max-active: 200
            # 配置获取连接等待超时的时间
            max-wait: 600000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            min-evictable-idle-time-millis: 300000
            #用来检测连接是否有效的sql，要求是一个查询语句
            validation-query: SELECT 'X' FROM DUAL
            #申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
            test-while-idle: true
            #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            test-on-borrow: false
            #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            test-on-return: false
            # 合并多个DruidDataSource的监控数据
            use-global-data-source-stat: false
            # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
            filters: slf4j
            # 打开PSCache，并且指定每个连接上PSCache的大小
            pool-prepared-statements: true
            #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
            max-pool-prepared-statement-per-connection-size: 20
            #最小空闲连接数保活
            keep-alive: true
            #以下为基于spring boot web的内嵌druid监控，若需开启请将三个值均置为true
            stat-view-servlet:
              enabled: false
            web-stat-filter:
              enabled: false
            filter:
              stat: false
            fail-fast: true
      platform:
        eureka: {{ .Values.global.middleware.defaultPlatform.eureka }}
        nacos:
          enable: {{ .Values.global.middleware.defaultPlatform.nacos.enable }}
          address: {{ .Values.global.middleware.defaultPlatform.nacos.address }}
        orbit:
          enable: {{ .Values.global.middleware.defaultPlatform.orbit.enable }}
        jupiter:
          #服务网格开关
          mesh:
            enable: {{ .Values.global.middleware.defaultJupiter.mesh.enable }}
          #单元化开关
          unitized:
            enable: {{ .Values.global.middleware.defaultJupiter.unitized.enable }}
            #需要按单元化逻辑调用的应用
            app: {{ .Values.global.middleware.defaultJupiter.unitized.app }}
        mars: {{ .Values.global.middleware.defaultPlatform.mars }}
        gateway: {{ .Values.global.middleware.defaultPlatform.gateway }}
        sonic:
          schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.schedulerAddresses }}
          lock:
            db-type: oracle
        mq:
          producer: {{ .Values.global.middleware.defaultPlatform.mq.producer }}
          consumer: {{ .Values.global.middleware.defaultPlatform.mq.consumer }}
        redis:
          host: {{ .Values.global.middleware.defaultPlatform.redis.host }}
          port: {{ .Values.global.middleware.defaultPlatform.redis.port }}
        apollo:
          enabled: {{ .Values.global.middleware.defaultPlatform.apollo.enable }}
          cluster: {{ .Values.global.middleware.defaultPlatform.apollo.cluster }}
          url: {{ .Values.global.middleware.defaultPlatform.apollo.url }}

      mybatis:
        #mybatis拓展属性文件路径
        config-location: classpath:mybatis/mybatis-config-oracle.xml

      #监控使用
      jupiter:
        mesh:
          enabled: {{ .Values.global.middleware.defaultJupiter.mesh.enable }} #网格调用需开启
        metrics:
          enable: {{ .Values.global.middleware.defaultJupiter.metrics.enable }}
        unitized:
          enabled: {{ .Values.global.middleware.defaultJupiter.unitized.enable }} #单元化需开启
          app: {{ .Values.global.middleware.defaultJupiter.unitized.app }}  #需要按单元化逻辑调用的应用

      management:
        endpoints:
          web:
            base-path: /actuator

      ## nacos配置
      galaxy:
        tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId }}
        profile: {{ .Values.global.middleware.defaultGalaxy.profile }}
        appId: {{ .Values.global.middleware.defaultGalaxy.appId }}
        appIdTwo: {{ .Values.global.middleware.defaultGalaxy.appIdTwo }}
        # 所属数据中心,多中心部署时配置
        dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter }}
        #单元化
        logicUnitId: {{ .Values.global.middleware.defaultGalaxy.logicUnitId }}
        phyUnitId: {{ .Values.global.middleware.defaultGalaxy.phyUnitId }}
