apiVersion: v1
kind: Service
metadata:
  name: {{ include "fpaas-rbmp-bm-online.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "fpaas-rbmp-bm-online.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "fpaas-rbmp-bm-online.selectorLabels" . | nindent 4 }}
