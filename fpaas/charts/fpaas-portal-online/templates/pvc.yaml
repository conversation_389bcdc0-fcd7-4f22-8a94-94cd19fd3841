{{- if .Values.persistentVolume.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "fpaas-portal-online.fullname" . }}-pvc
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "fpaas-portal-online.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistentVolume.accessMode }}
  {{- if .Values.persistentVolume.storageClass }}
  storageClassName: {{ .Values.persistentVolume.storageClass }}
  {{- end }}
  resources:
    requests:
      storage: {{ .Values.persistentVolume.size }}
{{- end }}
