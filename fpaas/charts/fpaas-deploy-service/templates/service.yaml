apiVersion: v1
kind: Service
metadata:
  name: {{ include "fpaas-deploy-service.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "fpaas-deploy-service.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "fpaas-deploy-service.selectorLabels" . | nindent 4 }}
