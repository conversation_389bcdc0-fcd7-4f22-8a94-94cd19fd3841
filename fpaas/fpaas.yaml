# FPAAS Application Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: fpaas-config
  namespace: fpaas
data:
  application.yml: |
    # FPAAS Global Configuration
    server:
      port: 8080
    
    spring:
      application:
        name: fpaas-parent
      profiles:
        active: fat
      cloud:
        nacos:
          discovery:
            server-addr: nacos-service:8848
            namespace: fpaas
    
    # Database configuration
    datasource:
      url: ********************************************
      username: fpaas_user
      password: fpaas_password
      driver-class-name: oracle.jdbc.OracleDriver
    
    # Redis configuration
    redis:
      host: redis-service
      port: 6379
      database: 0
    
    # Logging configuration
    logging:
      level:
        com.dcits.fpaas: INFO
        root: WARN
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
        file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
      file:
        name: /logs/fpaas.log
    
    # Management endpoints
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics
          base-path: /actuator
      endpoint:
        health:
          show-details: always
