---
# Source: rocketmq-standalone/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rocketmq-standalone-broker-config
  namespace: paas
data:
  broker.conf: |
    brokerClusterName = DefaultCluster
    brokerName = broker-a
    brokerId = 0
    deleteWhen = 04
    fileReservedTime = 48
    brokerRole = ASYNC_MASTER
    flushDiskType = ASYNC_FLUSH
    listenPort = 10911
---
# Source: rocketmq-standalone/templates/broker-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: rocketmq-standalone-broker
  namespace: paas
spec:
  type: ClusterIP
  selector:
    app: rocketmq-standalone-broker
  ports:
    - name: broker
      port: 10911
      targetPort: 10911
---
# Source: rocketmq-standalone/templates/nameserver-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: rocketmq-standalone-nameserver
  namespace: paas
spec:
  type: ClusterIP
  selector:
    app: rocketmq-standalone-nameserver
  ports:
    - name: nameserver
      port: 9876
      targetPort: 9876
---
# Source: rocketmq-standalone/templates/broker-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rocketmq-standalone-broker
  namespace: paas
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rocketmq-standalone-broker
  template:
    metadata:
      labels:
        app: rocketmq-standalone-broker
    spec:
      containers:
        - name: broker
          image: "apache/rocketmq:5.1.4"
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "-c"]
          args:
            - |
              export NAMESRV_ADDR=rocketmq-standalone-nameserver:9876 && \
              export JAVA_OPT_EXT="-Xms512m -Xmx512m -Xmn256m" && \
              exec /rocketmq/bin/mqbroker -n $$NAMESRV_ADDR -c /rocketmq/conf/broker.conf -p 10911
          ports:
            - containerPort: 10911
          volumeMounts:
            - name: broker-config
              mountPath: /rocketmq/conf/broker.conf
              subPath: broker.conf
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 256Mi
      volumes:
        - name: broker-config
          configMap:
            name: rocketmq-standalone-broker-config
---
# Source: rocketmq-standalone/templates/nameserver-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rocketmq-standalone-nameserver
  namespace: paas
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rocketmq-standalone-nameserver
  template:
    metadata:
      labels:
        app: rocketmq-standalone-nameserver
    spec:
      containers:
        - name: nameserver
          image: "apache/rocketmq:5.1.4"
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "-c"]
          args:
            - |
              exec /rocketmq/bin/mqnamesrv -c /rocketmq/conf/namesrv.conf
          ports:
            - containerPort: 9876
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 256Mi
