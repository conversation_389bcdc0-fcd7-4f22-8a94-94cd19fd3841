namespace: paas

image:
  repository: apache/rocketmq
  tag: 5.1.4
  pullPolicy: IfNotPresent

resources:
  limits:
    cpu: 1
    memory: 1Gi
  requests:
    cpu: 100m
    memory: 256Mi

persistence:
  enabled: false
  size: 5Gi

nameserver:
  replicas: 1
  service:
    type: ClusterIP
    port: 9876

broker:
  replicas: 1
  service:
    type: ClusterIP
    port: 10911
  config:
    BROKER_MEM: "-Xms512m -Xmx512m -Xmn256m"
    BROKER_ROLE: "ASYNC_MASTER"
    FLUSH_DISK_TYPE: "ASYNC_FLUSH"
