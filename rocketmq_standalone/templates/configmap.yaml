apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-broker-config
  namespace: {{ .Release.Namespace }}
data:
  broker.conf: |
    brokerClusterName = DefaultCluster
    brokerName = broker-a
    brokerId = 0
    deleteWhen = 04
    fileReservedTime = 48
    brokerRole = {{ .Values.broker.config.BROKER_ROLE }}
    flushDiskType = {{ .Values.broker.config.FLUSH_DISK_TYPE }}
    listenPort = 10911
