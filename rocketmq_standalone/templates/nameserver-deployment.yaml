apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-nameserver
  namespace: {{ .Release.Namespace }}
spec:
  replicas: {{ .Values.nameserver.replicas }}
  selector:
    matchLabels:
      app: {{ .Release.Name }}-nameserver
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}-nameserver
    spec:
      containers:
        - name: nameserver
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - |
              exec /rocketmq/bin/mqnamesrv -c /rocketmq/conf/namesrv.conf
          ports:
            - containerPort: 9876
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
