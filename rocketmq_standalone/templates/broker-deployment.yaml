apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-broker
  namespace: {{ .Release.Namespace }}
spec:
  replicas: {{ .Values.broker.replicas }}
  selector:
    matchLabels:
      app: {{ .Release.Name }}-broker
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}-broker
    spec:
      containers:
        - name: broker
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - |
              export NAMESRV_ADDR={{ .Release.Name }}-nameserver:9876 && \
              export JAVA_OPT_EXT="{{ .Values.broker.config.BROKER_MEM }}" && \
              exec /rocketmq/bin/mqbroker -n $$NAMESRV_ADDR -c /rocketmq/conf/broker.conf -p 10911
          ports:
            - containerPort: 10911
          volumeMounts:
            - name: broker-config
              mountPath: /rocketmq/conf/broker.conf
              subPath: broker.conf
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
        - name: broker-config
          configMap:
            name: {{ .Release.Name }}-broker-config
