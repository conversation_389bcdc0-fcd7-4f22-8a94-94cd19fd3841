---
# Source: nacos-standalone/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: nacos-standalone-nacos
  namespace: paas
spec:
  type: ClusterIP
  ports:
    - port: 8848
      targetPort: 8848
      protocol: TCP
      name: http
  selector:
    app: nacos-standalone-nacos
---
# Source: nacos-standalone/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nacos-standalone-nacos
  namespace: paas
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nacos-standalone-nacos
  template:
    metadata:
      labels:
        app: nacos-standalone-nacos
    spec:
      containers:
        - name: nacos
          image: "nacos/nacos-server:v3.0.2"
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8848
          env:
            - name: MODE
              value: "standalone"
            - name: NACOS_AUTH_ENABLE
              value: "false"
            - name: NACOS_AUTH_TOKEN
              value: "YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXpBQkNERUZHSElKSw=="
            - name: NACOS_AUTH_IDENTITY_KEY
              value: "nacos"
            - name: NACOS_AUTH_IDENTITY_VALUE
              value: "nacos"
            - name: JVM_XMS
              value: "256m"
            - name: JVM_XMX
              value: "512m"
            - name: JVM_XMN
              value: "256m"
            - name: JVM_MS
              value: "128m"
            - name: JVM_MMS
              value: "128m"
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 512Mi
