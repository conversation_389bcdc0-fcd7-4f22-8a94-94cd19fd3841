apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-nacos
  namespace: {{ .Values.namespace | default "default" }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Release.Name }}-nacos
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}-nacos
    spec:
      containers:
        - name: nacos
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: 8848
          env:
            - name: MODE
              value: "{{ .Values.env.MODE }}"
            - name: NACOS_AUTH_ENABLE
              value: "{{ .Values.env.NACOS_AUTH_ENABLE }}"
            - name: NACOS_AUTH_TOKEN
              value: "{{ .Values.env.NACOS_AUTH_TOKEN }}"
            - name: NACOS_AUTH_IDENTITY_KEY
              value: "{{ .Values.env.NACOS_AUTH_IDENTITY_KEY }}"
            - name: NACOS_AUTH_IDENTITY_VALUE
              value: "{{ .Values.env.NACOS_AUTH_IDENTITY_VALUE }}"
            - name: JVM_XMS
              value: "{{ .Values.env.JVM_XMS }}"
            - name: JVM_XMX
              value: "{{ .Values.env.JVM_XMX }}"
            - name: JVM_XMN
              value: "{{ .Values.env.JVM_XMN }}"
            - name: JVM_MS
              value: "{{ .Values.env.JVM_MS }}"
            - name: JVM_MMS
              value: "{{ .Values.env.JVM_MMS }}"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
