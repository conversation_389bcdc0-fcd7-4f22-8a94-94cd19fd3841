# Default values for microservices chart
# This is a YAML-formatted file.

# Global configuration
global:
  # Default namespace for all services
  namespace: "onebank"
  
  # Default image registry
  imageRegistry: ""
  
  # Default image pull policy
  imagePullPolicy: "IfNotPresent"
  
  # Default timezone
  timezone: "Asia/Shanghai"
  
  # Default init container image for log directory initialization
  initContainer:
    image: "busybox:1.35"
    imagePullPolicy: "IfNotPresent"

  # Node affinity configuration for pod distribution
  nodeAffinity:
    # Enable pod anti-affinity to spread pods across nodes
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Pod anti-affinity configuration
  podAntiAffinity:
    # Enable pod anti-affinity to avoid multiple pods on same node
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Default resource limits and requests
  defaultResources:
    limits:
      cpu: "1000m"
      memory: "1Gi"
    requests:
      cpu: "100m"
      memory: "128Mi"

  # Default security context
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000

  # Default database configuration
  database:
    dialect: mysql
    driverClassName: com.mysql.jdbc.Driver
    enabled: true
    initialSize: 1
    maxActive: 5
    maxIdle: 5
    minIdle: 1
    username: fpaas
    password: 1q2w3e4R!@#$
    url: ************************************************************************************************************************************************************************************************************
    validationQuery: 'select 1 '

  redis:
    cluster:
      enabled: false
    url: redis-host:6379
    password: Dcits123!@#

  galaxy:
  appId: ${spring.application.name}
  dataCenter: dc01
  logicUnitId: ''
  phyUnitId: ''
  profile: dev
  tenantId: fpaas
  biz-monitor:
    dir: /tmp/monitor/${spring.application.name}
    enable: false
    max-size: 10

# Micro-Services
fpaas-apigw-http:
  enabled: true
  ssl:
    enabled: true
    secretName: "fpaas-ssl-certs"
    mountPath: "/etc/ssl/certs"
    caCert: "ca.crt"
    clientCert: "redis.crt"
    clientKey: "redis.key"