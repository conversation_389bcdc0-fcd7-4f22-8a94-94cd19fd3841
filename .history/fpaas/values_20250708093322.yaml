# Default values for microservices chart
# This is a YAML-formatted file.

# Global configuration
global:
  # Default namespace for all services
  namespace: "onebank"
  
  # Default image registry
  imageRegistry: ""
  
  # Default image pull policy
  imagePullPolicy: "IfNotPresent"
  
  # Default timezone
  timezone: "Asia/Shanghai"
  
  # Default init container image for log directory initialization
  initContainer:
    image: "busybox:1.35"
    imagePullPolicy: "IfNotPresent"

  # Node affinity configuration for pod distribution
  nodeAffinity:
    # Enable pod anti-affinity to spread pods across nodes
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Pod anti-affinity configuration
  podAntiAffinity:
    # Enable pod anti-affinity to avoid multiple pods on same node
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Default resource limits and requests
  defaultResources:
    limits:
      cpu: "1000m"
      memory: "1Gi"
    requests:
      cpu: "100m"
      memory: "128Mi"

  # Default security context
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000


    # Default database configuration
    defaultDatabase:
      dialect: mysql
      driverClassName: com.mysql.jdbc.Driver
      enabled: true
      initialSize: 1
      maxActive: 5
      maxIdle: 5
      minIdle: 1
      username: fpaas
      password: 1q2w3e4R!@#$
      url: ************************************************************************************************************************************************************************************************************
      validationQuery: 'select 1 '

    # Default platform configuration
    defaultPlatform:
      eureka: ***************:9527
      nacos: 
        enable: true
        address: **************:8848
      orbit:
        enable: true
      mars: **************:9994
      gateway: dcits.cbs.gateway.sit1:8081
      sonic: 
        schedulerAddresses: **************:9999
        lock:
          db-type: oracle
      mq: 
        producer: **************:13070
        consumer: **************:13070
      redis: 
        host: **************
        port: 6379
      file:
        passwd: cbs001
        serverIp: ***************
        port: 5001
      apollo: 
        enable: false
        cluster: dev
        url: http://dcits.cbs.apoconf.sit1:8080
      skywalking:
        path: /app/dcits/app-run/agent
        url: dcits.cbs.skywalking.sit1:11800
      sso:
        url: http://**************:8085

    # Default sftp configuration
    defaultSftp:
      client:
        protocol: shareFile
        host: **************
        port: 22
        username: fat
        password: fat202101
        root: /home/<USER>/share/
        lroot: /home/<USER>/share/
        privateKey:
        passphrase:
        sessionStrictHostKeyChecking: no
        sessionConnectTimeout: 15000
        channelConnectedTimeout: 15000
        fileSplitDir: split
        fileSplitSize: 2
        fileSplitPercent: 20
        fileSplitLines: 20000

    # Default jupiter configuration
    defaultJupiter:
      mesh:
        enable: false
      metrics:
        enable: true
      unitized:
        enable: false
        app:

    # Default galaxy configuration
    defaultGalaxy:
      tenantId: online-batch
      profile: fat
      appId: SmartGl
      appIdTwo: SMARTGL-REPORT
      dataCenter: dc01
      logicUnitId:
      phyUnitId:

# Micro-Services
smartgl-batch:
  enabled: true
smartgl-manager:
  enabled: true
smartgl-online:
  enabled: true
smartgl-report:
  enabled: true 