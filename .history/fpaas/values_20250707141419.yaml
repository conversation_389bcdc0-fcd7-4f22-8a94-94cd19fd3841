# Global values for all fpaas services
global:
  namespace: fpaas
  middleware:
    defaultDatabase:
      username: fpaas_user
      password: fpaas_password
      host: ********************************************
    defaultPlatform:
      eureka: http://eureka-service:8761/eureka
      nacos:
        enable: true
        address: nacos-service:8848
      orbit:
        enable: true
      mars: http://mars-service:8080
      gateway: http://gateway-service:8080
      sonic:
        schedulerAddresses: sonic-service:8080
      mq:
        producer: true
        consumer: true
      redis:
        host: redis-service
        port: 6379
      apollo:
        enable: true
        cluster: default
        url: http://apollo-service:8080
    defaultSftp:
      client:
        protocol: sftp
        host: sftp-service
        port: 22
        username: fpaas
        password: fpaas123
        root: /home/<USER>
        lroot: /tmp
        passphrase: ""
        sessionStrictHostKeyChecking: false
        sessionConnectTimeout: 30000
        channelConnectedTimeout: 30000
        fileSplitDir: /tmp/split
        fileSplitSize: 100
        fileSplitPercent: 10
        fileSplitLines: 10000
    defaultJupiter:
      mesh:
        enable: true
      metrics:
        enable: true
      unitized:
        enable: true
        app: fpaas
    defaultGalaxy:
      tenantId: fpaas
      profile: fat
      appId: fpaas
      appIdTwo: fpaas-v2
      dataCenter: dc01
      logicUnitId: unit01
      phyUnitId: phy01

# Individual service configurations
fpaas-apigw-http:
  enabled: true
  
fpaas-file-upload:
  enabled: true
  
fpaas-deploy-service:
  enabled: true
  
fpaas-gateway-service:
  enabled: true
  
fpaas-rbmp-bm-online:
  enabled: true
  
fpaas-product-online:
  enabled: true
  
fpaas-portal-online:
  enabled: true
  
fpaas-strategy-online:
  enabled: true
