apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "fpaas-apigw-http.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "fpaas-apigw-http.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicas }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "fpaas-apigw-http.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "fpaas-apigw-http.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          {{- with .Values.livenessProbe }}
          livenessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.readinessProbe }}
          readinessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            {{- with .Values.volumeMounts }}
            {{- range . }}
            - name: {{ .name }}
              mountPath: {{ tpl (.mountPath | default "/logs") $ }}
            {{- end }}
            {{- end }}
            {{- if .Values.configMap.enabled }}
            - name: configmap-volume
              # TODO 配置文件挂载路径，取决于镜像怎么构建
              mountPath: {{ tpl .Values.configDirectory . }}
            {{- end }}
            {{- if .Values.persistentVolume.enabled }}
            - name: pv-volume
              mountPath: {{ .Values.persistentVolume.mountPath | default "/app/data" }}
            {{- end }}
            {{- if .Values.ssl.enabled }}
            - name: ssl-certs-volume
              mountPath: {{ .Values.ssl.mountPath }}
              readOnly: true
            {{- end }}
          {{- with .Values.env }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      volumes:
        {{- with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- if .Values.configMap.enabled }}
        - name: configmap-volume
          configMap:
            name: {{ include "fpaas-apigw-http.fullname" . }}-configmap
        {{- end }}
        {{- if .Values.persistentVolume.enabled }}
        - name: pv-volume
          persistentVolumeClaim:
            claimName: {{ include "fpaas-apigw-http.fullname" . }}-pvc
        {{- end }}
        {{- if .Values.ssl.enabled }}
        - name: ssl-certs-volume
          secret:
            secretName: {{ .Values.ssl.secretName }}
            defaultMode: 0400
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
