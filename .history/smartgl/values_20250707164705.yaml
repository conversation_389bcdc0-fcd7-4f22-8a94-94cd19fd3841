# Default values for microservices chart
# This is a YAML-formatted file.

# Global configuration
global:
  # Default namespace for all services
  namespace: "onebank"
  
  # Default image host
  imageHost: "************.dkr.ecr.ap-southeast-1.amazonaws.com"
  
  # Default image pull policy
  imagePullPolicy: "IfNotPresent"
  
  # Default timezone
  timezone: "Asia/Shanghai"
  
  # Default init container image for log directory initialization
  initContainer:
    image: "busybox:1.35"
    imagePullPolicy: "IfNotPresent"

  # Node affinity configuration for pod distribution
  nodeAffinity:
    # Enable pod anti-affinity to spread pods across nodes
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Pod anti-affinity configuration
  podAntiAffinity:
    # Enable pod anti-affinity to avoid multiple pods on same node
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Default resource limits and requests
  defaultResources:
    limits:
      cpu: "1000m"
      memory: "1Gi"
    requests:
      cpu: "100m"
      memory: "128Mi"

  # Default security context
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000

# Micro-Services
smartgl-batch:
  enabled: true
smartgl-manager:
  enabled: true
smartgl-online:
  enabled: true
smartgl-report:
  enabled: true 