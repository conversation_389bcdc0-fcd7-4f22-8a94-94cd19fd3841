---
# Source: redis-standalone/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-standalone-redis-config
  namespace: paas
data:
  redis.conf: |-
    appendonly yes
---
# Source: redis-standalone/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: redis-standalone-redis
  namespace: paas
spec:
  type: ClusterIP
  selector:
    app: redis-standalone-redis
  ports:
    - port: 6379
      targetPort: 6379
---
# Source: redis-standalone/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-standalone-redis
  namespace: paas
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-standalone-redis
  template:
    metadata:
      labels:
        app: redis-standalone-redis
    spec:
      containers:
        - name: redis
          image: "redis:7.2"
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 6379
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 128Mi
          volumeMounts:
            - name: config
              mountPath: /usr/local/etc/redis/redis.conf
              subPath: redis.conf
          args:
            - "redis-server"
            - "/usr/local/etc/redis/redis.conf"
      volumes:
        - name: config
          configMap:
            name: redis-standalone-redis-config
