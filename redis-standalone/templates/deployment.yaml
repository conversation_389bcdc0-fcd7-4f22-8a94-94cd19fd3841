apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-redis
  namespace: {{ .Values.namespace | default "default" }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Release.Name }}-redis
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}-redis
    spec:
      containers:
        - name: redis
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: 6379
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /usr/local/etc/redis/redis.conf
              subPath: redis.conf
          args:
            - "redis-server"
            - "/usr/local/etc/redis/redis.conf"
      volumes:
        - name: config
          configMap:
            name: {{ .Release.Name }}-redis-config
