apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "smartgl-online.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "smartgl-online.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "smartgl-online.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "smartgl-online.labels" . | nindent 8 }}
	{{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
      - command:
        - sh
        - -c
        - chown 1000:1000 {{ .Values.log.directory }}
        image: {{ tpl .Values.initImage.repository $ }}:{{ .Values.initImage.tag }}
        imagePullPolicy: {{ .Values.initImage.pullPolicy }}
        name: init-dir
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: {{ .Values.log.directory }}
          name: {{ .Values.log.volumeName }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ tpl .Values.image.repository $ }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          {{- with .Values.livenessProbe }}
          livenessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.readinessProbe }}
          readinessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: {{ .Values.log.volumeName }}
              mountPath: {{ .Values.log.directory }}
            # other volume mounts
          {{- with .Values.volumeMounts }}
            {{- range . }}
            - name: {{ .name }}
              mountPath: {{ tpl (.mountPath | default "/logs") $ }}
              {{- with .subPath }}
              subPath: {{ . }}
              {{- end }}
            {{- end }}
          {{- end }}
      volumes:
        - name: {{ .Values.log.volumeName }}
          hostPath:
            path: {{ .Values.log.hostDirectory }}
            type: DirectoryOrCreate
        # other volume mounts 
      {{- with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
