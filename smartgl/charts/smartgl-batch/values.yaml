ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
replicas: 2
# HPA configuration
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
# image configuration
initImage:
  repository: "{{ .Values.global.imageHost }}/onebank/busybox"
  tag: "latest"
  pullPolicy: "IfNotPresent"
image:
  repository: "{{ .Values.global.imageHost }}/onebank/smartgl-batch"
  tag: "0.0.5-alpha"
  pullPolicy: "IfNotPresent"
# config directory configuration
configDirectory: "/data/app/config/application-fat.yml"
# log directory configuration
log:
  directory: "/logs/smartgl-batch"
  volumeName: "log-volume"
  hostDirectory: "/home/<USER>/logs/smartgl-batch"

# Service configuration
service:
  type: "ClusterIP"
  port: 8080
  targetPort: 8080

# Resource configuration
resources:
  limits:
    cpu: "500m"
    memory: "512Mi"
  requests:
    cpu: "50m"
    memory: "64Mi"

# Additional volumes
volumeMounts:
  - name: "configuration-volume"
    mountPath: "{{ .Values.configDirectory | default \"/data/app/config/application-fat.yml\" }}"
    subPath: "application-fat.yml"
volumes:
  - name: "configuration-volume"
    configMap:
      name: "smartgl-smartgl-batch-configmap"

# # Liveness probe
# livenessProbe:
#   httpGet:
#     path: "/health"
#     port: 8080
#   initialDelaySeconds: 30
#   periodSeconds: 10

# # Readiness probe
# readinessProbe:
#   httpGet:
#     path: "/ready"
#     port: 8080
#   initialDelaySeconds: 5
#   periodSeconds: 5

# ConfigMap configuration
configMap:
  enabled: true
  data:
    application-fat.yml: |
      server:
        #web端口
        port: 12083
      spring:
        cloud:
          nacos:
            discovery:
              server-addr: {{ include "smartgl-batch.fullname" . }}-service.{{ .Values.global.namespace }}.svc.cluster.local:8848
              prefer-ip-address: false  # 禁用IP注册
              use-hostname: true        # 使用K8s Service名称
        datasource:
          username: {{ .Values.global.middleware.defaultDatabase.username }}
          password: {{ .Values.global.middleware.defaultDatabase.password }}
          driver-class-name: oracle.jdbc.OracleDriver
          url: {{ .Values.global.middleware.defaultDatabase.host }}
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            #初始化物理连接个数glCore
            initial-size: 20
            #最小连接池数量
            min-idle: 20
            #最大连接池数量
            max-active: 200
            # 配置获取连接等待超时的时间
            max-wait: 600000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            min-evictable-idle-time-millis: 300000
            #用来检测连接是否有效的sql，要求是一个查询语句,如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会其作用
            validation-query: SELECT 'X' FROM DUAL
            #申请连接的时候检测，如果空闲时间大于imeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
            test-while-idle: true
            #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            test-on-borrow: false
            #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            test-on-return: false
            # 通过connectProperties属性来打开mergeSql功能；慢SQL记录

            # 合并多个DruidDataSource的监控数据
            use-global-data-source-stat: false
            # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
            filters: slf4j
            # 打开PSCache，并且指定每个连接上PSCache的大小
            pool-prepared-statements: true
            #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
            max-pool-prepared-statement-per-connection-size: 20
            #最小空闲连接数保活
            keep-alive: true
            #以下为基于spring boot web的内嵌druid监控，若需开启请将三个值均置为true
            stat-view-servlet:
              enabled: false
            web-stat-filter:
              enabled: false
            filter:
              stat: false
            fail-fast: true
      platform:
        eureka: {{ .Values.global.middleware.defaultPlatform.eureka }}
        nacos:
          enable: {{ .Values.global.middleware.defaultPlatform.nacos.enable }}
          address: {{ .Values.global.middleware.defaultPlatform.nacos.address }}
        #kafka: dcits.cbs.kafka.sit1:9092
        orbit:
          enable: {{ .Values.global.middleware.defaultPlatform.orbit.enable }}
        jupiter:
          #服务网格开关
          mesh:
            enable: {{ .Values.global.middleware.defaultJupiter.mesh.enable }}
          #单元化开关
          unitized:
            enable: {{ .Values.global.middleware.defaultJupiter.unitized.enable }}
            #需要按单元化逻辑调用的应用
            app:
        mars: {{ .Values.global.middleware.defaultPlatform.mars }}
        gateway: {{ .Values.global.middleware.defaultPlatform.gateway }}
        sonic:
          schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.schedulerAddresses }}
          lock:
            db-type: oracle
        mq:
          producer: {{ .Values.global.middleware.defaultPlatform.mq.producer }}
          consumer: {{ .Values.global.middleware.defaultPlatform.mq.consumer }}
        redis:
          host: {{ .Values.global.middleware.defaultPlatform.redis.host }}
          port: {{ .Values.global.middleware.defaultPlatform.redis.port }}
        apollo:
          enabled: {{ .Values.global.middleware.defaultPlatform.apollo.enable }}
          cluster: {{ .Values.global.middleware.defaultPlatform.apollo.cluster }}
          url: {{ .Values.global.middleware.defaultPlatform.apollo.url }}

      mybatis:
        #mybatis拓展属性文件路径
        config-location: classpath:mybatis/mybatis-config-oracle.xml
        #mysql数据库驱动下，指定tdsql数据库，确保运行sql使用的是tdsql专用的方言sql
        #configurationProperties:
        #  databaseId: tdsql

      sftp:
        client:
          protocol: {{ .Values.global.middleware.defaultSftp.client.protocol }}
          # ip地址
          host: {{ .Values.global.middleware.defaultSftp.client.host }}
          # 端口
          port: {{ .Values.global.middleware.defaultSftp.client.port }}
          # 用户名
          username: {{ .Values.global.middleware.defaultSftp.client.username }}
          # 密码
          password: {{ .Values.global.middleware.defaultSftp.client.password }}
          # 根路径
          root: {{ .Values.global.middleware.defaultSftp.client.root }}
          # 本地根路径
          lroot: {{ .Values.global.middleware.defaultSftp.client.lroot }}
          # 密钥文件路径
          privateKey:
          # 密钥的密码
          passphrase: {{ .Values.global.middleware.defaultSftp.client.passphrase }}
          #se
          sessionStrictHostKeyChecking: {{ .Values.global.middleware.defaultSftp.client.sessionStrictHostKeyChecking }}
          # session连接超时时间
          sessionConnectTimeout: {{ .Values.global.middleware.defaultSftp.client.sessionConnectTimeout }}
          # channel连接超时时间
          channelConnectedTimeout: {{ .Values.global.middleware.defaultSftp.client.channelConnectedTimeout }}
          # 大文件拆分--拆分文件存放目录
          fileSplitDir: {{ .Values.global.middleware.defaultSftp.client.fileSplitDir }}
          # 大文件拆分--文件大小，单位MB，超过该配置大小在和sftp.client.fileSplitPercent配置项合并计算，决定是否拆分文件
          fileSplitSize: {{ .Values.global.middleware.defaultSftp.client.fileSplitSize }}
          # 大文件拆分--超出范围百分比，单位%，1-100的数，超出设定的文件大小后，超出部分超出对应百分比后才进行拆分
          fileSplitPercent: {{ .Values.global.middleware.defaultSftp.client.fileSplitPercent }}
          # 大文件拆分--拆分后文件记录行数
          fileSplitLines: {{ .Values.global.middleware.defaultSftp.client.fileSplitLines }}
      com:
        dcits:
          path:
            #该路径为GL本地文件存放路径，包括本地下载，及预备上传文件
            localFilePath: /home/<USER>/share/gl_file/
            #该路径为SIT环境共享文件夹，注意 当模式为sftp时远端路径不要配置第一个"/" eg: home/apps/file/ 并且路径从配置的root路径后自动拼接，当模式为shared时则必须配置 eg: /home/<USER>/file/，不受root路径配置影响
            targetFilePath: /home/<USER>/share/
            #该路径为存贷联合自动回收远端文件路径
            clTargetFilePath: /home/<USER>/file/gl_file/
            #该路径为与老核心短信通知远程路径
            clSmsFilePath: /home/<USER>/
            #用户路径
            userPath: /home/<USER>/

      #监控使用
      jupiter:
        mesh:
          enabled: {{ .Values.global.middleware.defaultJupiter.mesh.enable }} #网格调用需开启
        metrics:
          enable: {{ .Values.global.middleware.defaultJupiter.metrics.enable }}
        unitized:
          enabled: {{ .Values.global.middleware.defaultJupiter.unitized.enable }} #单元化需开启
          app: {{ .Values.global.middleware.defaultJupiter.unitized.app }}  #需要按单元化逻辑调用的应用

      management:
        endpoints:
          web:
            base-path: /actuator

      ## nacos配置
      galaxy:
        tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId }}
        profile: {{ .Values.global.middleware.defaultGalaxy.profile }}
        #  4.0配置
        #  availableZone: dc01
        #  gateway: http://comet-gateway-service
        #  tenant: nebula
        #  version: 1.1.0
        #  workspace: system
        appId: {{ .Values.global.middleware.defaultGalaxy.appId }}
        appIdTwo: {{ .Values.global.middleware.defaultGalaxy.appIdTwo }}
        # 所属数据中心,多中心部署时配置
        dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter }}
        #单元化
        logicUnitId: {{ .Values.global.middleware.defaultGalaxy.logicUnitId }}
        phyUnitId: {{ .Values.global.middleware.defaultGalaxy.phyUnitId }}

