{{- if .Values.configMap.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "smartgl-batch.fullname" . }}-configmap
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    app.kubernetes.io/name: {{ include "smartgl-batch.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: smartgl-batch
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  {{- range $key, $value := .Values.configMap.data }}
  {{ tpl $key $ }}: |
    {{- tpl $value $ | nindent 4 }}
  {{- end }}
{{- end }} 