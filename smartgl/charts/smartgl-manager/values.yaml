ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""
replicas: 2
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80
initImage:
  repository: "{{ .Values.global.imageHost }}/onebank/busybox"
  tag: "latest"
  pullPolicy: "IfNotPresent"
image:
  repository: "{{ .Values.global.imageHost }}/onebank/smartgl-manager"
  tag: "0.0.5-alpha"
  pullPolicy: "IfNotPresent"
configDirectory: "/data/app/config/bootstrap-{{ .Values.global.environment }}.yml"
log:
  directory: "/logs/smartgl-manager"
  volumeName: "log-volume"
  hostDirectory: "/home/<USER>/logs/smartgl-manager"

# Service configuration
service:
  type: "ClusterIP"
  port: 8080
  targetPort: 8080

# Resource configuration
resources:
  limits:
    cpu: "500m"
    memory: "512Mi"
  requests:
    cpu: "50m"
    memory: "64Mi"

# Persistent Volume configuration
persistentVolume:
  enabled: false
  storageClass: "" # 如需指定存储类，填写名称，否则留空
  accessMode: "ReadWriteOnce"
  size: "1Gi"
  mountPath: "/app/data" # 容器内挂载路径

# Additional volumes
volumeMounts:
  - name: "configuration-volume"
    mountPath: "{{ tpl .Values.configDirectory $ | default \"/data/app/config/bootstrap-fat.yml\" }}"
    subPath: "bootstrap-{{ .Values.global.environment }}.yml"
volumes:
  - name: "configuration-volume"
    configMap:
      name: "smartgl-smartgl-manager-configmap"

# # Liveness probe
# livenessProbe:
#   httpGet:
#     path: "/health"
#     port: 8080
#   initialDelaySeconds: 30
#   periodSeconds: 10

# # Readiness probe
# readinessProbe:
#   httpGet:
#     path: "/ready"
#     port: 8080
#   initialDelaySeconds: 5
#   periodSeconds: 5

# ConfigMap configuration
configMap:
  enabled: true
  data:
    bootstrap-{{ .Values.global.environment }}.yml: |
      server:
        #web端口
        port: 12083
      spring:
        cloud:
          nacos:
            discovery:
              server-addr: {{ include "smartgl-manager.fullname" . }}-service.{{ .Values.global.namespace }}.svc.cluster.local:8848
              prefer-ip-address: false  # 禁用IP注册
              use-hostname: true        # 使用K8s Service名称
        datasource:
          username: {{ .Values.global.middleware.defaultDatabase.username }}
          password: {{ .Values.global.middleware.defaultDatabase.password }}
          driver-class-name: oracle.jdbc.OracleDriver
          url: {{ .Values.global.middleware.defaultDatabase.host }}
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            #初始化物理连接个数glCore
            initial-size: 20
            #最小连接池数量
            min-idle: 20
            #最大连接池数量
            max-active: 200
            # 配置获取连接等待超时的时间
            max-wait: 600000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            min-evictable-idle-time-millis: 300000
            #用来检测连接是否有效的sql，要求是一个查询语句,如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会其作用
            validation-query: SELECT 'X' FROM DUAL
            #申请连接的时候检测，如果空闲时间大于imeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
            test-while-idle: true
            #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            test-on-borrow: false
            #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            test-on-return: false
            # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
            connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
            # 配置DruidStatFilter
            web-stat-filter:
              enabled: true
              url-pattern: /*
              exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
            # 配置DruidStatViewServlet
            stat-view-servlet:
              enabled: true
              url-pattern: /druid/*
              # IP白名单(没有配置或者为空，则允许所有访问)
              allow: 127.0.0.1
              # IP黑名单 (存在共同时，deny优先于allow)
              deny:
              # 禁用HTML页面上的"Reset All"功能
              reset-enable: false
              # 登录名
              login-username: admin
              # 登录密码
              login-password: 123456
            # 配置DruidStatFilter
            filter:
              stat:
                enabled: true
                # 慢SQL记录
                log-slow-sql: true
                slow-sql-millis: 1000
                merge-sql: true
              wall:
                enabled: true
              log4j:
                enabled: true
        jpa:
          hibernate:
            ddl-auto: update
          show-sql: true
          properties:
            hibernate:
              dialect: org.hibernate.dialect.Oracle12cDialect
              format_sql: true
        jackson:
          date-format: yyyy-MM-dd HH:mm:ss
          time-zone: GMT+8
        servlet:
          multipart:
            max-file-size: 10MB
            max-request-size: 10MB
      mybatis:
        #mybatis拓展属性文件路径
        config-location: classpath:mybatis/mybatis-config-oracle.xml
        #mysql数据库驱动下，指定tdsql数据库，确保运行sql使用的是tdsql专用的方言sql
        #configurationProperties:
        #  databaseId: tdsql
      
      sftp:
        client:
          protocol: shareFile
          # ip地址
          host: **************
          # 端口
          port: 22
          # 用户名
          username: fat
          # 密码
          password: fat202101
          # 根路径
          root: /home/<USER>/share/
          # 本地根路径
          lroot: /home/<USER>/share/
          # 密钥文件路径
          privateKey:
          # 密钥的密码
          passphrase: 
          #se
          sessionStrictHostKeyChecking: false
          # session连接超时时间
          sessionConnectTimeout: 15000
          # channel连接超时时间
          channelConnectedTimeout: 15000
          # 大文件拆分--拆分文件存放目录
          fileSplitDir: split
          # 大文件拆分--文件大小，单位MB，超过该配置大小在和sftp.client.fileSplitPercent配置项合并计算，决定是否拆分文件
          fileSplitSize: 2
          # 大文件拆分--超出范围百分比，单位%，1-100的数，超出设定的文件大小后，超出部分超出对应百分比后才进行拆分
          fileSplitPercent: 20
          # 大文件拆分--拆分后文件记录行数
          fileSplitLines: 20000
      
      ## nacos配置
      galaxy:
        tenantId: online-batch
        profile: {{ .Values.global.environment }}
        #  4.0配置
        #  availableZone: dc01
        #  gateway: http://comet-gateway-service
        #  tenant: nebula
        #  version: 1.1.0
        #  workspace: system
        appId: SmartGl
        appIdTwo: SMARTGL-REPORT
        # 所属数据中心,多中心部署时配置
        dataCenter: dc01
        #单元化
        logicUnitId: 
        phyUnitId:

