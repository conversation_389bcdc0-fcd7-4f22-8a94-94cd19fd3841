# Libra Helm Chart

这是一个用于部署 Libra 微服务的 Helm Chart，管理两个子 charts：`libra` 和 `libra-manager`。

## 目录结构

```
libra/
├── Chart.yaml                    # 父 chart 定义
├── values.yaml                   # 全局默认配置
├── libra.yaml                    # 示例配置文件
├── charts/                       # 子 charts 目录
│   ├── libra/                    # libra 子 chart
│   │   ├── Chart.yaml
│   │   ├── values.yaml
│   │   └── templates/
│   │       ├── _helpers.tpl
│   │       ├── configmap.yaml
│   │       ├── deployment.yaml
│   │       ├── service.yaml
│   │       └── NOTES.txt
│   └── libra-manager/            # libra-manager 子 chart
│       ├── Chart.yaml
│       ├── values.yaml
│       └── templates/
│           ├── _helpers.tpl
│           ├── configmap.yaml
│           ├── deployment.yaml
│           ├── service.yaml
│           └── NOTES.txt
└── README.md
```

## 功能特性

- **ConfigMap 支持**: 每个服务都有独立的配置管理
- **Deployment 管理**: 支持副本数、资源限制、健康检查等
- **Service 配置**: 支持 ClusterIP、NodePort、LoadBalancer 等服务类型
- **全局配置**: 统一管理镜像仓库、命名空间、环境等全局设置
- **环境适配**: 支持多环境配置（fat、dev、sit、uat、prod）

## 安装使用

### 1. 基本安装

```bash
# 进入 chart 目录
cd onebank/deployment/galaxy/libra

# 安装 chart
helm install libra . -n onebank --create-namespace
```

### 2. 使用自定义配置

```bash
# 使用示例配置文件
helm install libra . -f libra.yaml -n onebank --create-namespace

# 或者使用自定义配置
helm install libra . \
  --set global.environment=sit \
  --set libra.replicaCount=3 \
  --set libra-manager.replicaCount=2 \
  -n onebank --create-namespace
```

### 3. 升级部署

```bash
helm upgrade libra . -f libra.yaml -n onebank
```

### 4. 卸载

```bash
helm uninstall libra -n onebank
```

## 配置说明

### 全局配置 (global)

- `namespace`: Kubernetes 命名空间
- `imageHost`: 镜像仓库地址
- `environment`: 环境标识 (fat/dev/sit/uat/prod)
- `middleware`: 中间件配置（数据库、Nacos、Redis等）

### 子服务配置

每个子服务支持以下配置：

- `enabled`: 是否启用该服务
- `replicaCount`: 副本数量
- `image`: 镜像配置
- `service`: 服务配置
- `resources`: 资源限制
- `configMap`: 配置文件内容

## 示例配置

参考 `libra.yaml` 文件中的示例配置：

```yaml
global:
  namespace: "onebank"
  environment: "boxfat"
  
libra:
  enabled: true
  replicaCount: 2
  service:
    port: 12080

libra-manager:
  enabled: true
  replicaCount: 2
  service:
    port: 12083
```

## 服务端口

- **libra**: 12080
- **libra-manager**: 12083

## 注意事项

1. 确保 Kubernetes 集群已正确配置
2. 镜像需要提前推送到指定的镜像仓库
3. 数据库连接信息需要根据实际环境调整
4. 建议在生产环境中使用具体的镜像标签而非 `latest`

## 故障排查

```bash
# 查看 pods 状态
kubectl get pods -n onebank

# 查看服务状态
kubectl get svc -n onebank

# 查看配置
kubectl get configmap -n onebank

# 查看日志
kubectl logs -f deployment/libra-libra -n onebank
kubectl logs -f deployment/libra-libra-manager -n onebank
```
