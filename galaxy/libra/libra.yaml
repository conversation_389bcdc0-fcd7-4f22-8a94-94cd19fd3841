# Example configuration for libra deployment
# This file can be used with: helm install libra . -f libra.yaml

global:
  namespace: "onebank"
  environment: "boxfat"
  imageHost: "************.dkr.ecr.ap-southeast-1.amazonaws.com"
  
  middleware:
    defaultDatabase:
      host: "jdbc:oracle:thin:@**************:1521/dcits"
      username: "ENS_FAT"
      password: "<PERSON><PERSON>(7f003151f1d531102ab890b5513fb180)"
    
    defaultGalaxy:
      tenantId: online-batch
      profile: fat
      appId: Libra
      appIdTwo: LIBRA-MANAGER
      dataCenter: dc01

libra:
  enabled: true
  replicaCount: 2
  image:
    tag: "0.0.1-alpha"
  service:
    port: 12080
  resources:
    limits:
      cpu: "1000m"
      memory: "1Gi"
    requests:
      cpu: "100m"
      memory: "256Mi"

libra-manager:
  enabled: true
  replicaCount: 2
  image:
    tag: "0.0.1-alpha"
  service:
    port: 12083
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "128Mi"
