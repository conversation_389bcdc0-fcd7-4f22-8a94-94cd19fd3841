apiVersion: v1
kind: Service
metadata:
  name: {{ include "libra-manager.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "libra-manager.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "libra-manager.selectorLabels" . | nindent 4 }}
