apiVersion: v1
kind: Service
metadata:
  name: {{ include "libra.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "libra.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "libra.selectorLabels" . | nindent 4 }}
