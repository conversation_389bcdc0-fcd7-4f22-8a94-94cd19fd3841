replicaCount: 1

image:
  repository: sonic-scheduler
  tag: latest
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 9901

resources: {}

applicationConfig: |-
  servicename: sonic-scheduler
  server:
    port: 9901
    servlet:
      context-path: /sonic
      encoding:
        charset: UTF-8
        force: true
    messages:
      encoding: UTF-8
  spring:
    application:
      name: ${servicename}
    profiles:
      active: generic
    main:
      allow-bean-definition-overriding: true
    messages:
      encoding: UTF-8
      basename: i18n/messages
    datasource:
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initialSize: 10
        minIdle: 20
        maxActive: 200
        maxWait: 10000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        filters: stat,wall,log4j2
        maxPoolPreparedStatementPerConnectionSize: 20
        useGlobalDataSourceStat: true
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
        web-stat-filter:
          enabled: true
          url-pattern: "/*"
          exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
    flyway:
      enabled: false
      table: sonic_flyway_schema_history
      check-location: false
      batch-enabled: true
      repair: false
      force-repeat: false
      baseline-on-migrate: true
      validate-on-migrate: true
      baseline-version: 1.0.0.1
      locations:
        - classpath:db/migration/{vendor}
    cloud:
      nacos:
        discovery:
          enabled: false
  auth:
    whitelist:
      - /druid/**
      - /actuator/**
      - /sonic/**
      - /**/lhdmon/**
      - /sdk/**
  sonic:
    scheduler:
      mybatis:
        databaseIdProvider:
          enabled: true
      lock: db
      step:
        maxParallelDispatch: 50
        dispatcherType: randomRule
      synchronizers:
        - name: nodeInfoSynchronize
          timer: 100
        - name: unitInfoSynchronize
          timer: 60
  mybatis:
    mapper-locations: classpath:mapper/*Mapper.xml
  logging:
    level:
      root: info
  apollo:
    bootstrap:
      namespaces: application,sonic-server.params-public
  app:
    id: sonic-server
    cluster: default
  eureka:
    client:
      registry-fetch-interval-seconds: 5
    instance:
      prefer-ip-address: true
  management:
    health:
      redis:
        enabled: false
    metrics:
      export:
        prometheus:
          enabled: true
    endpoints:
      web:
        base-path: /lhdmon
        exposure:
          include: '*'
  alarm:
    enabled: true
    url: http://aries-alert-center:8091/api/v1/customs/alerts
    alarmSets:
      - JF
      - SF
    info:
      - tenant: fcsjlwj1gk9s
        profile: y6vklwj509pg
        integrateSecret: 1132033688960237568
        recoverType: automatic
        alertStrategySecret: 1132034038714859520
        handleWay: occur
      - tenant: galaxy
        profile: FAT
        integrateSecret: 785187720367439872FAT
        recoverType: manual
        alertStrategySecret: 785205069304823808
        handleWay: occur
      - tenant: galaxy
        profile: SIT
        integrateSecret: 785187720367439872
        recoverType: manual
        alertStrategySecret: 785205069304823808
        handleWay: occur

bootstrapConfig: |-
  spring:
    cloud:
      nacos:
        config:
          enabled: false
          server-addr: nacos.dev.dc01.galaxy.com:38848
          group: galaxy.FAT.dc01
          tenantId: galaxy
          workspace: FAT 