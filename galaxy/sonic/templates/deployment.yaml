apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "sonic-scheduler.fullname" . }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ include "sonic-scheduler.name" . }}
  template:
    metadata:
      labels:
        app: {{ include "sonic-scheduler.name" . }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.service.port }}
          volumeMounts:
            - name: config
              mountPath: /app/conf
      volumes:
        - name: config
          configMap:
            name: {{ include "sonic-scheduler.fullname" . }}-config 