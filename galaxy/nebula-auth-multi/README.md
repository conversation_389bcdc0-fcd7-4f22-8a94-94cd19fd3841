# Nebula Auth Multi Helm Chart

This Helm chart deploys the Nebula Auth Multi service, an authentication and authorization service for the Nebula platform.

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- MySQL database
- Redis instance
- (Optional) Nacos service discovery

## Installing the Chart

To install the chart with the release name `nebula-auth`:

```bash
helm install nebula-auth ./nebula-auth-multi
```

To install with custom values:

```bash
helm install nebula-auth ./nebula-auth-multi -f my-values.yaml
```

## Uninstalling the Chart

To uninstall/delete the `nebula-auth` deployment:

```bash
helm uninstall nebula-auth
```

## Configuration

The following table lists the configurable parameters and their default values.

### Application Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `app.name` | Application name | `auth-service` |
| `app.profile` | Spring profile | `container` |
| `app.contextPath` | Application context path | `/oms/uaa` |

### Image Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `image.repository` | Image repository | `nebula-auth-multi` |
| `image.tag` | Image tag | `latest` |
| `image.pullPolicy` | Image pull policy | `IfNotPresent` |

### Service Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `service.type` | Service type | `ClusterIP` |
| `service.port` | Service port | `8085` |
| `service.targetPort` | Container port | `8085` |

### Database Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `database.host` | Database host | `mysql-service` |
| `database.port` | Database port | `3306` |
| `database.name` | Database name | `auth` |
| `database.username` | Database username | `AUTH` |
| `database.password` | Database password | `""` |

### Redis Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `redis.host` | Redis host | `redis-service` |
| `redis.port` | Redis port | `6379` |
| `redis.password` | Redis password | `""` |

### Galaxy Platform Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `app.galaxy.tenant` | Galaxy tenant | `nebula` |
| `app.galaxy.workspace` | Galaxy workspace | `system` |
| `app.galaxy.region` | Galaxy region | `default` |
| `app.galaxy.availabilityZone` | Galaxy availability zone | `dc01` |

### Resource Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `resources.limits.cpu` | CPU limit | `2000m` |
| `resources.limits.memory` | Memory limit | `4Gi` |
| `resources.requests.cpu` | CPU request | `500m` |
| `resources.requests.memory` | Memory request | `2Gi` |

### JVM Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `app.jvm.xms` | Initial heap size | `2g` |
| `app.jvm.xmx` | Maximum heap size | `2g` |
| `app.jvm.metaspaceSize` | Initial metaspace size | `128m` |
| `app.jvm.maxMetaspaceSize` | Maximum metaspace size | `512m` |

## Examples

### Basic Installation

```bash
helm install nebula-auth ./nebula-auth-multi \
  --set database.password=mypassword \
  --set redis.password=redispassword
```

### Production Installation with Custom Values

Create a `production-values.yaml` file:

```yaml
replicaCount: 3

image:
  repository: my-registry/nebula-auth-multi
  tag: "v1.0.0"

database:
  host: prod-mysql.example.com
  password: "secure-password"

redis:
  host: prod-redis.example.com
  password: "redis-password"

ingress:
  enabled: true
  className: nginx
  hosts:
    - host: auth.example.com
      paths:
        - path: /
          pathType: Prefix

resources:
  limits:
    cpu: 4000m
    memory: 8Gi
  requests:
    cpu: 1000m
    memory: 4Gi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70

persistence:
  enabled: true
  size: 50Gi
  storageClass: fast-ssd
```

Then install:

```bash
helm install nebula-auth ./nebula-auth-multi -f production-values.yaml
```

### Development Installation

```bash
helm install nebula-auth-dev ./nebula-auth-multi \
  --set app.profile=dev \
  --set database.host=localhost \
  --set database.password=devpassword \
  --set redis.host=localhost \
  --set resources.limits.memory=2Gi \
  --set resources.requests.memory=1Gi
```

## Health Checks

The chart includes comprehensive health checks:

- **Liveness Probe**: Checks if the application is running
- **Readiness Probe**: Checks if the application is ready to serve traffic

Both probes use the Spring Boot Actuator health endpoint.

## Monitoring

The chart supports monitoring through:

- Prometheus metrics (enabled by default)
- Spring Boot Actuator endpoints
- Application logs

## Security

The chart implements several security best practices:

- Non-root user execution
- Read-only root filesystem (configurable)
- Security contexts
- Network policies (optional)
- Secret management for sensitive data

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify database host and credentials
   - Check network connectivity
   - Ensure database exists and user has proper permissions

2. **Redis Connection Issues**
   - Verify Redis host and password
   - Check if Redis is accessible from the cluster

3. **Application Won't Start**
   - Check pod logs: `kubectl logs -f deployment/nebula-auth`
   - Verify configuration in ConfigMap
   - Check resource limits

### Debugging Commands

```bash
# Check pod status
kubectl get pods -l app.kubernetes.io/name=nebula-auth-multi

# View pod logs
kubectl logs -f deployment/nebula-auth-multi

# Check configuration
kubectl get configmap nebula-auth-multi-config -o yaml

# Check secrets
kubectl get secret nebula-auth-multi-db-secret -o yaml

# Port forward for local testing
kubectl port-forward svc/nebula-auth-multi 8080:8085
```

## Contributing

Please read the contributing guidelines before submitting pull requests.

## License

This chart is licensed under the Apache License 2.0.
