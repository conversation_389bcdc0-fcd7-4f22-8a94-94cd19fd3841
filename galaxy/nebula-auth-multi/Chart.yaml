apiVersion: v2
name: nebula-auth-multi
description: A Helm chart for Nebula Auth Multi Service - Authentication and authorization service for the Nebula platform
type: application
version: 0.1.0
appVersion: "1.0.0"
home: https://github.com/dcits/nebula-auth-multi
sources:
  - https://github.com/dcits/nebula-auth-multi
maintainers:
  - name: DCITS Team
    email: <EMAIL>
keywords:
  - authentication
  - authorization
  - spring-boot
  - java
  - oauth2
  - jwt
annotations:
  category: Authentication
dependencies: []
