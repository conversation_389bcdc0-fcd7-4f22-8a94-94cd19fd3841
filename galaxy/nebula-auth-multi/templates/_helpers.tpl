{{/*
Expand the name of the chart.
*/}}
{{- define "nebula-auth-multi.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "nebula-auth-multi.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "nebula-auth-multi.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "nebula-auth-multi.labels" -}}
helm.sh/chart: {{ include "nebula-auth-multi.chart" . }}
{{ include "nebula-auth-multi.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "nebula-auth-multi.selectorLabels" -}}
app.kubernetes.io/name: {{ include "nebula-auth-multi.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "nebula-auth-multi.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "nebula-auth-multi.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the image name
*/}}
{{- define "nebula-auth-multi.image" -}}
{{- printf "%s:%s" .Values.image.repository (.Values.image.tag | default .Chart.AppVersion) }}
{{- end }}

{{/*
Create database URL
*/}}
{{- define "nebula-auth-multi.databaseUrl" -}}
{{- printf "*************************************************************************************************************************************************************************" .Values.database.host (.Values.database.port | int) .Values.database.name }}
{{- end }}

{{/*
Create JVM options
*/}}
{{- define "nebula-auth-multi.jvmOptions" -}}
{{- $jvm := .Values.app.jvm -}}
{{- $logDir := printf "%s/%s" .Values.app.logging.path .Values.app.name -}}
{{- printf "-Xms%s -Xmx%s -XX:MetaspaceSize=%s -XX:MaxMetaspaceSize=%s -Xss%s %s -Xloggc:%s/gc.log %s -XX:HeapDumpPath=%s/heapdump %s" $jvm.xms $jvm.xmx $jvm.metaspaceSize $jvm.maxMetaspaceSize $jvm.xss $jvm.gcOptions $logDir $jvm.heapDumpOptions $logDir $jvm.additionalOptions }}
{{- end }}

{{/*
Create Nacos server address
*/}}
{{- define "nebula-auth-multi.nacosServerAddr" -}}
{{- if .Values.nacos.enabled }}
{{- .Values.nacos.serverAddr }}
{{- else }}
{{- "" }}
{{- end }}
{{- end }}

{{/*
Create Galaxy tenant namespace
*/}}
{{- define "nebula-auth-multi.galaxyNamespace" -}}
{{- printf "%s_%s" .Values.app.galaxy.tenant .Values.app.galaxy.workspace }}
{{- end }}

{{/*
Create Galaxy discovery group
*/}}
{{- define "nebula-auth-multi.galaxyGroup" -}}
{{- printf "%s-%s" .Values.app.galaxy.tenant .Values.app.galaxy.workspace }}
{{- end }}

{{/*
Create ConfigMap name
*/}}
{{- define "nebula-auth-multi.configMapName" -}}
{{- printf "%s-config" (include "nebula-auth-multi.fullname" .) }}
{{- end }}

{{/*
Create Secret name for database
*/}}
{{- define "nebula-auth-multi.databaseSecretName" -}}
{{- printf "%s-db-secret" (include "nebula-auth-multi.fullname" .) }}
{{- end }}

{{/*
Create Secret name for Redis
*/}}
{{- define "nebula-auth-multi.redisSecretName" -}}
{{- printf "%s-redis-secret" (include "nebula-auth-multi.fullname" .) }}
{{- end }}

{{/*
Create PVC name
*/}}
{{- define "nebula-auth-multi.pvcName" -}}
{{- printf "%s-logs" (include "nebula-auth-multi.fullname" .) }}
{{- end }}
