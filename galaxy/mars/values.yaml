serviceName: mars

namespace: galaxy

replicaCount: 1

initImage:
  repository: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox"
  tag: "latest"
  pullPolicy: "IfNotPresent"

image:
  repository: "************.dkr.ecr.ap-southeast-1.amazonaws.com/galaxy/mars"
  tag: "0.0.1-alpha"
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 9014
  targetPort: 9014

resources:
  limits:
    cpu: 1000m
    memory: 4Gi
  requests:
    cpu: 200m
    memory: 256Mi

server:
  port: 9014
  # servlet:
  #   context-path: /mars
  #   encoding:
  #     charset: utf-8
  #     force: true

auth:
  address: onebank-paas.dcits.sg:7080
#   whitelist:
#     - /druid/**
#     - /actuator/**
#     - /**/lhdmon/**

spring:
  application:
    name: mars
  profiles:
    active: generic
  # main:
  #   allow-circular-references: true
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************************
    username: root
    password: rootpasswd#45r
    druid:
      initialSize: 10
      minIdle: 5
      maxActive: 100
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
  #     validation-query: SELECT 1 FROM DUAL
  #     test-while-idle: true
  #     test-on-borrow: false
  #     test-on-return: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 100
      connectProperties:
        druid:
          stat:
            mergeSql: true
            slowSqlMillis: 1000
      useGlobalDataSourceStat: true
      filters: stat,slf4j
      statViewServlet:
        enabled: true
        urlPattern: /druid/*
        resetEnable: true
  # flyway:
  #   enabled: false
  #   check-location: false
  #   batch-enabled: true
  #   repair: false
  #   force-repeat: false
  #   baseline-on-migrate: true
  #   validate-on-migrate: true
  #   baseline-version: 1.0.0.1
  #   locations:
  #     - classpath:db/migration/{vendor}
  cloud:
    nacos:
      discovery:
        serverAddr: ac8a7a76d075b4765b0178c380ebb136-b746ee1afeeecde6.elb.ap-southeast-1.amazonaws.com:8848
        # port: 8087
        enabled: false
        # group: galaxy-FAT
        # metadata:
        #   tenant: galaxy
        #   profile: FAT
        #   group: galaxy-FAT
        #   applicationName: mars
      config:
        enabled: false
        serverAddr: ac8a7a76d075b4765b0178c380ebb136-b746ee1afeeecde6.elb.ap-southeast-1.amazonaws.com:8848

sequences:
  service:
    coreThreads: 200
    maxThreads: 200
#     queueSize: 200
#     oracleCoreThreads: 0
#     oracleMaxThreads: 200
    activeFillCoreThreads: 50
    activeFillMaxThreads: 200
#     activeFillQueueSize: -1
#   heartbeat:
#     interval: 3000
#     reconnectInterval: 3000
#     failTimes: 3
#   #message:
#   #  timeout: 1000
#   election:
#     lease: 30000
#     renewalInternal: 5000
#     renewalInternalWhenFaild: 2000
  server:
    port: 9994
    groupid: MARS_DEMO_GROUP
    autocreatetype: DBTS

# pagehelper:
#   reasonable: true
#   auto-dialect: true
#   helper-dialect: mysql

# eureka:
#   client:
#     registry-fetch-interval-seconds: 5

# apollo:
#   bootstrap:
#     namespaces: application,mars-server.public
# app:
#   id: mars-server
#   cluster: default

# mybatis:
#   mapper-locations: classpath*:mapper/*Mapper.xml
#   configuration:
#     map-underscore-to-camel-case: true

logging:
  file:
    path: /apps/logs
#   level:
#     root: info

# 访问地址http://ip:${server.port}}/mars/swagger-ui/index.html#/
# springdoc:
#   swagger-ui:
#     enabled: true
#   packages-to-scan: com.dcits.mars.manager

# management:
#   health:
#     redis:
#       enabled: false
#   metrics:
#     export:
#       prometheus:
#         enabled: true
#   endpoints:
#     web:
#       base-path: /lhdmon
#       exposure:
#         include: "*"
