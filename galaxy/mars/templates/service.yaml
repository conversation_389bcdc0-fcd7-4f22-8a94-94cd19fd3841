apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.serviceName }}
  namespace: {{ .Values.namespace | default "default" }}
spec:
  type: {{ .Values.service.type }}
  selector:
    app: mars
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
    - port: {{ .Values.sequences.server.port }}
      targetPort: {{ .Values.sequences.server.port }}
      protocol: TCP
      name: sequence
