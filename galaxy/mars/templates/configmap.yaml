apiVersion: v1
kind: ConfigMap
metadata:
  name: mars-config
  namespace: {{ .Values.namespace | default "default" }}
data:
  application.yml: |
    servicename: {{ .Values.serviceName }}

    server:
      port: {{ .Values.server.port }}
      servlet:
        context-path: /mars
        encoding:
          charset: utf-8
          force: true

    auth:
      whitelist:
        - /druid/**
        - /actuator/**
        - /**/lhdmon/**

    spring:
      application:
        name: {{ .Values.spring.application.name }}
      profiles:
        # dev（开发环境）, prod（生产环境）
        # start.sh $1 环境参数启动不通的profiles，本地测试请通过VM参数指定profile，e.g -Dspring.profiles.active=dev
        active: {{ .Values.spring.profiles.active }}
      main:
        allow-circular-references: true
      datasource:
        druid:
          initial-size: 10 #初始化物理连接个数
          min-idle: 10 #最小连接池数量
          max-active: 100 #最大连接池数量
          max-wait: 5000 # 配置获取连接等待超时的时间
          time-between-eviction-runs-millis: 300000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位是毫秒
          validation-query: SELECT 1 FROM DUAL #用来检测连接是否有效的sql，要求是一个查询语句,如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会起作用
          test-while-idle: true #申请连接的时候检测，如果空闲时间大于imeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
          test-on-borrow: false #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
          test-on-return: false #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
          pool-prepared-statements: true # 打开PSCache，并且指定每个连接上PSCache的大小
          max-pool-prepared-statement-per-connection-size: 20 #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
          connect-properties:
            druid:
              stat:
                mergeSql: true
                slowSqlMillis: 1000 # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
          use-global-data-source-stat: true # 合并多个DruidDataSource的监控数据
          filters: stat,config,wall
          #connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAK1g4VT3BL2XeZSyT3WpMHN6XtRfrhE4FmnwH6f+JvqLxZCN/+UdBD6w8N9dB+PYnRxSDpRwWKwWUIx9KqwXio0CAwEAAQ==
      flyway:
        enabled: false
        check-location: false
        batch-enabled: true
        repair: false
        force-repeat: false
        baseline-on-migrate: true
        validate-on-migrate: true
        baseline-version: 1.0.0.1
        locations:
          - classpath:db/migration/{vendor}
      cloud:
        nacos:
          discovery:
            server-addr: {{ .Values.spring.cloud.nacos.discovery.serverAddr }}
            #使用管理访问端口，如不集成运维平台应用监控则可默认使用server.port, 监控探测数据拉取端口， context-path默认为/
            port: 8087
            #集成应用监控时开启
            enabled: false
            #与运维平台租户、环境信息配置
            group: galaxy-FAT
            metadata:
              tenant: galaxy
              profile: FAT
              group: galaxy-FAT
              #固定配置
              applicationName: mars

    sequences:
      service:
        coreThreads: 50
        maxThreads: 200
        queueSize: 200
        oracleCoreThreads: 0
        oracleMaxThreads: 200
        activeFillCoreThreads: 50
        activeFillMaxThreads: 100
        activeFillQueueSize: -1
      heartbeat:
        interval: 3000 #心跳检查间隔ms
        reconnectInterval: 3000
        failTimes: 3 #心跳检查失败几次后将连接标记为不可用
      #message:
      #  timeout: 1000 #通讯超时时间ms
      election:
        lease: 30000
        renewalInternal: 5000
        renewalInternalWhenFaild: 2000

    #分页查询配置
    pagehelper:
      reasonable: true
      auto-dialect: true
      helper-dialect: mysql

    eureka:
      client:
        registry-fetch-interval-seconds: 5

    #apollo统一配置参数
    apollo:
      bootstrap:
        namespaces: application,mars-server.public
    app:
      id: mars-server
      cluster: default

    mybatis:
      mapper-locations: classpath*:mapper/*Mapper.xml
      configuration:
        map-underscore-to-camel-case: true

    #root日志级别设置
    logging:
      level:
        root: info

    #访问地址http://ip:${server.port}}/mars/swagger-ui/index.html#/
    springdoc:
      swagger-ui:
        #是否开启
        enabled: true
      packages-to-scan: com.dcits.mars.manager

    #健康监测,针对运维指标采集提供独立端口与路径
    management:
      health:
        redis:
          enabled: false
      metrics:
        export:
          prometheus:
            enabled: true
      endpoints:
        web:
          base-path: /lhdmon
          exposure:
            include: '*'

  application-generic.yml: |
    spring:
      cloud:
        nacos:
          config: #配置中心
            enabled: ${SPRING_CLOUD_NACOS_CONFIG_ENABLED:false}
            server-addr: ${SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR:http://nacos-X-config.galaxy4:38848}
            tenant: ${galaxy.tenant:nebula}
            workspace: ${galaxy.workspace:system}
          discovery: #注册中心
            enabled: ${SPRING_CLOUD_NACOS_DISCOVERY_ENABLED:false}
            server-addr: ${SISPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR:http://nacos-X-register.galaxy4:8848}
            tenant: ${galaxy.tenant:nebula}
            workspace: ${galaxy.workspace:system}
      security:
        oauth2:
          resourceserver:
            opaque-token:
              # 配置 nebula-auth 验证 token 地址
              introspection-uri: ${nebula.auth.service.base-url}/oms/uaa/user/auth
      datasource:
        driver-class-name: ${SPRING_DATASOURCE_DRIVER_CLASS_NAME}
        url: ${SPRING_DATASOURCE_URL}
        username: ${SPRING_DATASOURCE_USERNAME}
        password: ${SPRING_DATASOURCE_PASSWORD}
        druid:
          #初始化物理连接个数
          initial-size: ${SPRING_DATASOURCE_DRUID_INITIAL_SIZE}
          #最小连接池数量
          min-idle: ${SPRING_DATASOURCE_DRUID_MIN_IDLE}
          #最大连接池数量
          max-active: ${SPRING_DATASOURCE_DRUID_MAX_ACTIVE}
          # 配置获取连接等待超时的时间
          max-wait: ${SPRING_DATASOURCE_DRUID_MAX_WAIT}
          # 打开PSCache，并且指定每个连接上PSCache的大小
          pool-prepared-statements: ${SPRING_DATASOURCE_DRUID_POOL_PREPARED_STATEMENTS}
          #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
          max-pool-prepared-statement-per-connection-size: ${SPRING_DATASOURCE_DRUID_MAX_POOL_PREPARED_STATEMENT_PER_CONNECTION_SIZE}
          # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          time-between-eviction-runs-millis: ${SPRING_DATASOURCE_DRUID_TIME_BETWEEN_EVICTION_RUNS_MILLIS}
          # 配置一个连接在池中最小生存的时间，单位是毫秒
          min-evictable-idle-time-millis: ${SPRING_DATASOURCE_DRUID_MIN_EVICTABLE_IDLE_TIME_MILLS}
          # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
          connection-properties:
            druid:
              stat:
                mergeSql: ${SRPING_DATASOURCE_DRUID_STAT_MERGE_SQL}
                slowSqlMillis: ${SRPING_DATASOURCE_DRUID_SLOWSQLMILLIS}
          # 合并多个DruidDataSource的监控数据
          use-global-data-source-stat: ${SPRING_DATASOURCE_DRUID_USE_GLOBAL_DATA_STAT}
          # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
          filters: ${SPRING_DATASOURCE_DRUID_FILTERS}
          #最小空闲连接数保活
          stat-view-servlet:
            #是否启用Druid监控页面的Servlet,生产环境建议启用
            enabled: ${SPRING_DATASOURCE_DRUID_STAT_VIEW_SERVLET_ENABLED}
            #根据配置的url-pattern来访问内置Druid监控页面
            url-pattern: ${SPRING_DATASOURCE_DRUID_STAT_VIEW_SERVLET_URL_PATTERN}
            #设置为true时Druid监控所有计数器清零,重新计数
            reset-enable: ${SPRING_DATASOURCE_DRUID_STAT_VIEW_SERVLET_RESET_ENABLE}

    sequences:
      service:
        coreThreads: ${SEQUENCE_SERVICE_CORE_THREADS}
        maxThreads: ${SEQUENCE_SERVICE_MAX_THREADS}
        activeFillCoreThreads: ${SEQUENCE_SERVICE_ACTIVE_FILL_CORE_THREADS}
        activeFillMaxThreads: ${SEQUENCE_SERVICE_ACTIVE_FILL_MAX_THREADS}
      server:
        port: 9994
        groupid: ${SEQUENCE_SERVER_GROUPID:MARS_DEMO_GROUP} #集群组，同一组ID之间才能构成集群
        autocreatetype: ${SEQUENCE_SERVER_AUTOCREATETYPE:DBTS}

    #审计相关配置
    nebula:
      auth:
        service:
          base-url: http://${AUTH_ADDRESS:nebula-vm.dcits.com}
          appName: 分布式序列
      audit:
        enabled: true

    eureka:
      client:
        enabled: false

    #api后端鉴权
    security:
      oauth2:
        resource:
          id: nebula
          # Oauth 用户信息地址
          user-info-uri: ${nebula.auth.service.base-url}/oms/uaa/user/auth
          prefer-token-info: false
          filter-order: 3
          permission:
            enabled: true
          whitelist:   ####4.4.1修改点
            - /**/actuator/**
            - /druid/**
            - /*.html
            - /v2/**
            - /webjars/**
            - /swagger-resources/**
            - /sba-settings.js
    swagger:
      enable: true

    logging:
      file:
        path: ${LOGGING_FILE_PATH:/apps/logs}

  bootstrap.yml: |
    spring:
      cloud:
        nacos:
          config:
            enabled: false
            server-addr: nacos.dev.dc01.galaxy.com:38848
            group: galaxy.FAT.dc01
            tenantId: galaxy # 租户
            workspace: FAT # 3.0-环境；4.0-工作空间

  logback-spring.xml: |
    <configuration scan="true" scanPeriod="5 seconds">

      <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
      <include resource="jupiter-logback-platform.xml" />
      <springProperty scope="context" name="logbackLoglevel" source="logging.level.root"
        defaultValue="info"/>
      <springProperty scope="context" name="applicationName" source="spring.application.name"/>
    <!--  <property name="logPath" value="/apps/logs"/>-->
      <property name="CONSOLE_LOG_PATTERN"
        value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
      <!-- Console 输出设置 -->
      <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
          <pattern>${CONSOLE_LOG_PATTERN}</pattern>
          <charset>utf8</charset>
        </encoder>
      </appender>
      <!-- File 输出设置 -->
      <!--<appender name="ROLLING" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <prudent>true</prudent>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
          <fileNamePattern>${logPath}/${applicationName}/%d{yyyyMMdd}/${applicationName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
          <maxHistory>30</maxHistory>
          <timeBasedFileNamingAndTriggeringPolicy
            class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
            <maxFileSize>10MB</maxFileSize>
          </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
          <pattern>#[%date{ISO8601}]|%-5level|[%thread]|%logger{0}|%F.%M:%L| &ndash;&gt; %msg%n</pattern>
        </encoder>
      </appender>-->

      <root level="${logbackLoglevel}">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="JUPITER_LOG_FILE"/>
      </root>
    </configuration>
