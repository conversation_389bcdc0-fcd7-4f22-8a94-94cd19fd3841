apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.serviceName }}
  namespace: {{ .Values.namespace | default "default" }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: mars
  template:
    metadata:
      labels:
        app: mars
    spec:
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /apps/logs/
          image: {{ .Values.initImage.repository }}:{{ .Values.initImage.tag }}
          imagePullPolicy: {{ .Values.initImage.pullPolicy }}
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /apps/logs/
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.server.port }}
              protocol: TCP
            - name: sequence
              containerPort: {{ .Values.sequences.server.port }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: SPRING_CLOUD_NACOS_CONFIG_ENABLED
              value: {{ .Values.spring.cloud.nacos.config.enabled | quote }}
            - name: SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR
              value: {{ .Values.spring.cloud.nacos.config.serverAddr | quote }}
            - name: SPRING_CLOUD_NACOS_DISCOVERY_ENABLED
              value: {{ .Values.spring.cloud.nacos.discovery.enabled | quote }}
            - name: SISPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR
              value: {{ .Values.spring.cloud.nacos.discovery.serverAddr | quote }}
            - name: SPRING_DATASOURCE_DRIVER_CLASS_NAME
              value: {{ .Values.spring.datasource.driverClassName | quote }}
            - name: SPRING_DATASOURCE_URL
              value: {{ .Values.spring.datasource.url | quote }}
            - name: SPRING_DATASOURCE_USERNAME
              value: {{ .Values.spring.datasource.username | quote }}
            - name: SPRING_DATASOURCE_PASSWORD
              value: {{ .Values.spring.datasource.password | quote }}
            - name: SPRING_DATASOURCE_DRUID_INITIAL_SIZE
              value: {{ .Values.spring.datasource.druid.initialSize | quote }}
            - name: SPRING_DATASOURCE_DRUID_MIN_IDLE
              value: {{ .Values.spring.datasource.druid.minIdle | quote }}
            - name: SPRING_DATASOURCE_DRUID_MAX_ACTIVE
              value: {{ .Values.spring.datasource.druid.maxActive | quote }}
            - name: SPRING_DATASOURCE_DRUID_MAX_WAIT
              value: {{ .Values.spring.datasource.druid.maxWait | quote }}
            - name: SPRING_DATASOURCE_DRUID_POOL_PREPARED_STATEMENTS
              value: {{ .Values.spring.datasource.druid.poolPreparedStatements | quote }}
            - name: SPRING_DATASOURCE_DRUID_MAX_POOL_PREPARED_STATEMENT_PER_CONNECTION_SIZE
              value: {{ .Values.spring.datasource.druid.maxPoolPreparedStatementPerConnectionSize | quote }}
            - name: SPRING_DATASOURCE_DRUID_TIME_BETWEEN_EVICTION_RUNS_MILLIS
              value: {{ .Values.spring.datasource.druid.timeBetweenEvictionRunsMillis | quote }}
            - name: SPRING_DATASOURCE_DRUID_MIN_EVICTABLE_IDLE_TIME_MILLS
              value: {{ .Values.spring.datasource.druid.minEvictableIdleTimeMillis | quote }}
            - name: SRPING_DATASOURCE_DRUID_STAT_MERGE_SQL
              value: {{ .Values.spring.datasource.druid.connectProperties.druid.stat.mergeSql | quote }}
            - name: SRPING_DATASOURCE_DRUID_SLOWSQLMILLIS
              value: {{ .Values.spring.datasource.druid.connectProperties.druid.stat.slowSqlMillis | quote }}
            - name: SPRING_DATASOURCE_DRUID_USE_GLOBAL_DATA_STAT
              value: {{ .Values.spring.datasource.druid.useGlobalDataSourceStat | quote }}
            - name: SPRING_DATASOURCE_DRUID_FILTERS
              value: {{ .Values.spring.datasource.druid.filters | quote }}
            - name: SPRING_DATASOURCE_DRUID_STAT_VIEW_SERVLET_ENABLED
              value: {{ .Values.spring.datasource.druid.statViewServlet.enabled | quote }}
            - name: SPRING_DATASOURCE_DRUID_STAT_VIEW_SERVLET_URL_PATTERN
              value: {{ .Values.spring.datasource.druid.statViewServlet.urlPattern | quote }}
            - name: SPRING_DATASOURCE_DRUID_STAT_VIEW_SERVLET_RESET_ENABLE
              value: {{ .Values.spring.datasource.druid.statViewServlet.resetEnable | quote }}
            - name: SEQUENCE_SERVICE_CORE_THREADS
              value: {{ .Values.sequences.service.coreThreads | quote }}
            - name: SEQUENCE_SERVICE_MAX_THREADS
              value: {{ .Values.sequences.service.maxThreads | quote }}
            - name: SEQUENCE_SERVICE_ACTIVE_FILL_CORE_THREADS
              value: {{ .Values.sequences.service.activeFillCoreThreads | quote }}
            - name: SEQUENCE_SERVICE_ACTIVE_FILL_MAX_THREADS
              value: {{ .Values.sequences.service.activeFillMaxThreads | quote }}
            - name: SEQUENCE_SERVER_GROUPID
              value: {{ .Values.sequences.server.groupid | quote }}
            - name: SEQUENCE_SERVER_AUTOCREATETYPE
              value: {{ .Values.sequences.server.autocreatetype | quote }}
            - name: AUTH_ADDRESS
              value: {{ .Values.auth.address | quote }}
            - name: LOGGING_FILE_PATH
              value: {{ .Values.logging.file.path | quote }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          volumeMounts:
            - name: logs
              mountPath: /apps/logs
            - name: application
              mountPath: /apps/conf/application.yml
              subPath: application.yml
            - name: application-generic
              mountPath: /apps/conf/application-generic.yml
              subPath: application-generic.yml
            - name: bootstrap
              mountPath: /apps/conf/bootstrap.yml
              subPath: bootstrap.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/{{ .Values.serviceName }}
            type: DirectoryOrCreate
        - name: application
          configMap:
            name: mars-config
        - name: application-generic
          configMap:
            name: mars-config
        - name: bootstrap
          configMap:
            name: mars-config
