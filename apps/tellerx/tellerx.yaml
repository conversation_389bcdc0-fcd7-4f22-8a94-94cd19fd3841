---
# Source: tellerx/charts/operationsm-receipt/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-operationsm-receipt-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: operationsm-receipt
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: operationsm-receipt
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    # spring:
    #   cache:
    #     type: redis
    #   #redis配置
    #   redis:
    #     ## Redis数据库索引（默认为0）
    #     database: 0
    #     ## Redis服务器地址
    #     host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
    #     ## Redis服务器连接端口
    #     port: 3306
    #     ## Redis服务器连接密码（默认为空）
    #     password: 
    #     lettuce:
    #       pool:
    #         ## 连接池最大连接数（使用负值表示没有限制）
    #         #spring.redis.pool.max-active=8
    #         max-active: 20
    #         ## 连接池最大阻塞等待时间（使用负值表示没有限制）
    #         #spring.redis.pool.max-wait=-1
    #         max-wait: 3000
    #         ## 连接池中的最大空闲连接
    #         #spring.redis.pool.max-idle=8
    #         max-idle: 5
    #         ## 连接池中的最小空闲连接
    #         #spring.redis.pool.min-idle=0
    #         min-idle: 0
    #     ## 连接超时时间（毫秒）
    #     timeout: 
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_venus-paperless   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_venus-paperless  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: centralauth
    
    #心跳检查开关
    eureka:
      client:
        healthcheck:
          enabled: false
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
---
# Source: tellerx/charts/venus-app-auth/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-app-auth-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-app-auth
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-app-auth
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      #redis配置
      redis:
        ## Redis数据库索引（默认为0）
        database: 0
        ## Redis服务器地址
        host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
        ## Redis服务器连接端口
        port: 3306
        ## Redis服务器连接密码（默认为空）
        password: 
        lettuce:
          pool:
            ## 连接池最大连接数（使用负值表示没有限制）
            #spring.redis.pool.max-active=8
            max-active: 20
            ## 连接池最大阻塞等待时间（使用负值表示没有限制）
            #spring.redis.pool.max-wait=-1
            max-wait: 3000
            ## 连接池中的最大空闲连接
            #spring.redis.pool.max-idle=8
            max-idle: 5
            ## 连接池中的最小空闲连接
            #spring.redis.pool.min-idle=0
            min-idle: 0
        ## 连接超时时间（毫秒）
        timeout: 
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_auth   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_auth  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: auth
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
---
# Source: tellerx/charts/venus-app-custom/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-app-custom-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-app-custom
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-app-custom
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      #redis配置
      redis:
        ## Redis数据库索引（默认为0）
        database: 0
        ## Redis服务器地址
        host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
        ## Redis服务器连接端口
        port: 3306
        ## Redis服务器连接密码（默认为空）
        password: 
        lettuce:
          pool:
            ## 连接池最大连接数（使用负值表示没有限制）
            #spring.redis.pool.max-active=8
            max-active: 20
            ## 连接池最大阻塞等待时间（使用负值表示没有限制）
            #spring.redis.pool.max-wait=-1
            max-wait: 3000
            ## 连接池中的最大空闲连接
            #spring.redis.pool.max-idle=8
            max-idle: 5
            ## 连接池中的最小空闲连接
            #spring.redis.pool.min-idle=0
            min-idle: 0
        ## 连接超时时间（毫秒）
        timeout: 
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_venus-custom   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_venus-custom  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: custom
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
---
# Source: tellerx/charts/venus-app-depn/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-app-depn-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-app-depn
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-app-depn
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      #redis配置
      redis:
        ## Redis数据库索引（默认为0）
        database: 0
        ## Redis服务器地址
        host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
        ## Redis服务器连接端口
        port: 3306
        ## Redis服务器连接密码（默认为空）
        password: 
        lettuce:
          pool:
            ## 连接池最大连接数（使用负值表示没有限制）
            #spring.redis.pool.max-active=8
            max-active: 20
            ## 连接池最大阻塞等待时间（使用负值表示没有限制）
            #spring.redis.pool.max-wait=-1
            max-wait: 3000
            ## 连接池中的最大空闲连接
            #spring.redis.pool.max-idle=8
            max-idle: 5
            ## 连接池中的最小空闲连接
            #spring.redis.pool.min-idle=0
            min-idle: 0
        ## 连接超时时间（毫秒）
        timeout: 
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_depn  #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_depn  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: depn
    
    #心跳检查开关
    eureka:
      client:
        healthcheck:
          enabled: false
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
---
# Source: tellerx/charts/venus-app-pmw/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-app-pmw-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-app-pmw
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-app-pmw
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_venus-pmw   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_venus-pmw  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: pmw
    
    #心跳检查开关
    eureka:
      client:
        healthcheck:
          enabled: false
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
---
# Source: tellerx/charts/venus-app-sysparam/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-app-sysparam-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-app-sysparam
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-app-sysparam
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      cache:
        type: redis
      #redis配置
      redis:
        ## Redis数据库索引（默认为0）
        database: 0
        ## Redis服务器地址
        host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
        ## Redis服务器连接端口
        port: 3306
        ## Redis服务器连接密码（默认为空）
        password: 
        lettuce:
          pool:
            ## 连接池最大连接数（使用负值表示没有限制）
            #spring.redis.pool.max-active=8
            max-active: 20
            ## 连接池最大阻塞等待时间（使用负值表示没有限制）
            #spring.redis.pool.max-wait=-1
            max-wait: 3000
            ## 连接池中的最大空闲连接
            #spring.redis.pool.max-idle=8
            max-idle: 5
            ## 连接池中的最小空闲连接
            #spring.redis.pool.min-idle=0
            min-idle: 0
        ## 连接超时时间（毫秒）
        timeout: 
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_sysparam   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_sysparam  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: sysparam
    
    #心跳检查开关
    eureka:
      client:
        healthcheck:
          enabled: false
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
---
# Source: tellerx/charts/venus-app-user/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-app-user-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-app-user
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-app-user
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      #redis配置
      redis:
        ## Redis数据库索引（默认为0）
        database: 0
        ## Redis服务器地址
        host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
        ## Redis服务器连接端口
        port: 3306
        ## Redis服务器连接密码（默认为空）
        password: 
        lettuce:
          pool:
            ## 连接池最大连接数（使用负值表示没有限制）
            #spring.redis.pool.max-active=8
            max-active: 20
            ## 连接池最大阻塞等待时间（使用负值表示没有限制）
            #spring.redis.pool.max-wait=-1
            max-wait: 3000
            ## 连接池中的最大空闲连接
            #spring.redis.pool.max-idle=8
            max-idle: 5
            ## 连接池中的最小空闲连接
            #spring.redis.pool.min-idle=0
            min-idle: 0
        ## 连接超时时间（毫秒）
        timeout: 
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_venus-usermanager   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_venus-usermanager  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: user
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
---
# Source: tellerx/charts/venus-core-configservice/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-core-configservice-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-core-configservice
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-core-configservice
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
---
# Source: tellerx/charts/venus-core-environment/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-core-environment-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-core-environment
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-core-environment
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      http: 
        encoding:
          charset: UTF-8
          force: true
          enabled: true
      servlet:
        multipart:
          max-file-size: 50MB
          max-request-size: 50MB
      #redis配置
      redis:
        ## Redis数据库索引（默认为0）
        database: 0
        ## Redis服务器地址
        host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
        ## Redis服务器连接端口
        port: 3306
        ## Redis服务器连接密码（默认为空）
        password: 
        lettuce:
          pool:
            ## 连接池最大连接数（使用负值表示没有限制）
            #spring.redis.pool.max-active=8
            max-active: 20
            ## 连接池最大阻塞等待时间（使用负值表示没有限制）
            #spring.redis.pool.max-wait=-1
            max-wait: 3000
            ## 连接池中的最大空闲连接
            #spring.redis.pool.max-idle=8
            max-idle: 5
            ## 连接池中的最小空闲连接
            #spring.redis.pool.min-idle=0
            min-idle: 0
        ## 连接超时时间（毫秒）
        timeout: 
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_scheduler   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: P_teller_scheduler  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: scheduler
    
    #心跳检查开关
    eureka:
      client:
        healthcheck:
          enabled: false
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
---
# Source: tellerx/charts/venus-core-gateway/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-core-gateway-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-core-gateway
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-core-gateway
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 7073
      tomcat: 
        accept-count: 5000 
        max-threads: 5000
        max-connections: 20000
      ssl:
        key-store: classpath:venustest.p12
        key-store-password: 123456
        key-store-type: PKCS12
        keyAlias: server
        enabled: false
    
    #心跳检查开关
    eureka:
      client:
        healthcheck:
          enabled: false
    
    spring:
      redis:
        ## Redis数据库索引（默认为0）
        database: 0
        ## Redis服务器地址
        host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
        ## Redis服务器连接端口
        port: 3306
        timeout: 60s  # 数据库连接超时时间，2.0 中该参数的类型为Duration，这里在配置的时候需要指明单位
        # 连接池配置，2.0中直接使用jedis或者lettuce配置连接池
        jedis:
          pool:
            # 最大空闲连接数
            max-idle: 500
            # 最小空闲连接数
            min-idle: 50
            # 等待可用连接的最大时间，负数为不限制
            max-wait: -1s
            # 最大活跃连接数，负数为不限制
            max-active: -1
    
    venus:
      digitalsignature: true
    
    ratelimiter-conf:
      #配置限流参数与RateLimiterConf类映射
      rateLimitMap:
        #格式为：routeid(gateway配置routes时指定的).系统名称.replenishRate(流速)/burstCapacity令牌桶大小
        app-service.TELLER_USER.replenishRate: 8000
        app-service.TELLER_USER.burstCapacity: 10000
    
    ribbon:
      nacos:
        enabled: false
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
        redis:
          enabled: false
---
# Source: tellerx/charts/venus-core-journal/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-core-journal-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-core-journal
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-core-journal
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      http: 
        encoding:
          charset: UTF-8
          force: true
          enabled: true
      servlet:
        multipart:
          max-file-size: 50MB
          max-request-size: 50MB
    
    venus:
      journal-thred-pool: 200
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_journal   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_journal  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: journal
    
    feign:
      client:
        config:
          venus-cms:
            connect-timeout: 60000
            read-timeout: 60000           
      httpclient:
        connection-timeout: 60000
        max-connections: 500
      hystrix:
        enabled: false
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
---
# Source: tellerx/charts/venus-core-scenecontrol/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-core-scenecontrol-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-core-scenecontrol
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-core-scenecontrol
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      cache:
        type: redis
      #redis配置
      redis:
        ## Redis数据库索引（默认为0）
        database: 0
        ## Redis服务器地址
        host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
        ## Redis服务器连接端口
        port: 3306
        ## Redis服务器连接密码（默认为空）
        password: 
        lettuce:
          pool:
            ## 连接池最大连接数（使用负值表示没有限制）
            #spring.redis.pool.max-active=8
            max-active: 20
            ## 连接池最大阻塞等待时间（使用负值表示没有限制）
            #spring.redis.pool.max-wait=-1
            max-wait: 3000
            ## 连接池中的最大空闲连接
            #spring.redis.pool.max-idle=8
            max-idle: 5
            ## 连接池中的最小空闲连接
            #spring.redis.pool.min-idle=0
            min-idle: 0
        ## 连接超时时间（毫秒）
        timeout: 
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_scenecrl   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_scenecrl  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: scenecrl
    
    feign:
      client:
        config:
          default:
            connect-timeout: 60000
            read-timeout: 60000
      httpclient:
        connection-timeout: 60000
        max-connections: 500
      hystrix:
        enabled: false
    
    #断路器配置
    hystrix:
      metrics:
        enabled: true
        #刷新间隔
        polling-interval-ms: 2000
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
      health:
        rabbit:
          enabled: false    #rabbit健康检查关闭
    
    #编译器选择
      customparam:
        viewcompiler: "vue"   #vue or react
---
# Source: tellerx/charts/venus-core-taskcontrol/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tellerx-venus-core-taskcontrol-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: venus-core-taskcontrol
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/component: venus-core-taskcontrol
    app.kubernetes.io/managed-by: Helm
data:
  application.yml: |
    server:
      port: 8080
    
    venus:
      digitalsignature: true
      mq:
        enable: false  #消息中间件是否启动
        type: rabbitmq      #消息中间件的类型rocketmq、rabbitmq、sofamq
        producer:
          groupName: P_teller_taskcontrol   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
        consumer:
          groupName: S_teller_taskcontrol  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
          namesrvAddr: localhost:5672
          userName: guest
          password: guest
          qindex: taskcontrol
    
    spring:
      devtools:
        restart:
          enabled: true
      servlet:
        multipart:
          max-file-size: 50MB
          max-request-size: 50MB
      #redis配置
      redis:
        ## Redis数据库索引（默认为0）
        database: 0
        ## Redis服务器地址
        host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
        ## Redis服务器连接端口
        port: 3306
        ## Redis服务器连接密码（默认为空）
        password: 
        jedis:
          pool:
            ## 连接池最大连接数（使用负值表示没有限制）
            #spring.redis.pool.max-active=8
            maxActive: 8
            ## 连接池最大阻塞等待时间（使用负值表示没有限制）
            #spring.redis.pool.max-wait=-1
            maxWait: -1
            ## 连接池中的最大空闲连接
            #spring.redis.pool.max-idle=8
            maxIdle: 8
            ## 连接池中的最小空闲连接
            #spring.redis.pool.min-idle=0
            minIdle: 0
        ## 连接超时时间（毫秒）
        timeout: 12000
    
    #健康检查
    management:
      endpoints:
        web:
          exposure:
            include: "health,info"
      server:
        servlet:
          context-path: /
        ssl:
          enabled: false
      endpoint:
        health:
          show-details: always   ##never 概要  always 详细
---
# Source: tellerx/charts/operationsm-receipt/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-operationsm-receipt
  namespace: onebank
  labels:
    helm.sh/chart: operationsm-receipt-0.1.0
    app.kubernetes.io/name: operationsm-receipt
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.0.1"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: operationsm-receipt
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-app-auth/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-app-auth
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-auth-0.1.0
    app.kubernetes.io/name: venus-app-auth
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.3.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-app-auth
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-app-custom/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-app-custom
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-custom-0.1.0
    app.kubernetes.io/name: venus-app-custom
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-app-custom
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-app-depn/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-app-depn
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-depn-0.1.0
    app.kubernetes.io/name: venus-app-depn
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.1.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-app-depn
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-app-pmw/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-app-pmw
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-pmw-0.1.0
    app.kubernetes.io/name: venus-app-pmw
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-app-pmw
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-app-sysparam/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-app-sysparam
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-sysparam-0.1.0
    app.kubernetes.io/name: venus-app-sysparam
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.4.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-app-sysparam
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-app-user/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-app-user
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-user-0.1.0
    app.kubernetes.io/name: venus-app-user
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.3.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-app-user
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-core-configservice/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-core-configservice
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-configservice-0.1.0
    app.kubernetes.io/name: venus-core-configservice
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.2.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-core-configservice
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-core-environment/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-core-environment
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-environment-0.1.0
    app.kubernetes.io/name: venus-core-environment
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.4.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-core-environment
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-core-gateway/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-core-gateway
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-gateway-0.1.0
    app.kubernetes.io/name: venus-core-gateway
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.1.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 7073
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-core-gateway
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-core-journal/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-core-journal
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-journal-0.1.0
    app.kubernetes.io/name: venus-core-journal
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.3.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-core-journal
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-core-scenecontrol/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-core-scenecontrol
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-scenecontrol-0.1.0
    app.kubernetes.io/name: venus-core-scenecontrol
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.5.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-core-scenecontrol
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/venus-core-taskcontrol/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tellerx-venus-core-taskcontrol
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-taskcontrol-0.1.0
    app.kubernetes.io/name: venus-core-taskcontrol
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.2.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: venus-core-taskcontrol
    app.kubernetes.io/instance: tellerx
---
# Source: tellerx/charts/operationsm-receipt/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-operationsm-receipt
  namespace: onebank
  labels:
    helm.sh/chart: operationsm-receipt-0.1.0
    app.kubernetes.io/name: operationsm-receipt
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.0.1"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: operationsm-receipt
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: operationsm-receipt-0.1.0
        app.kubernetes.io/name: operationsm-receipt
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.0.1"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/operationsm-receipt
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/operationsm-receipt
      containers:
        - name: operationsm-receipt
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/operationsm-receipt:0.0.15-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/operationsm-receipt
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/operationsm-receipt
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-operationsm-receipt-configmap
---
# Source: tellerx/charts/venus-app-auth/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-app-auth
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-auth-0.1.0
    app.kubernetes.io/name: venus-app-auth
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.3.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-app-auth
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-app-auth-0.1.0
        app.kubernetes.io/name: venus-app-auth
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.3.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-authcenter
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-authcenter
      containers:
        - name: venus-app-auth
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-app-auth:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-authcenter
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-authcenter
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-app-auth-configmap
---
# Source: tellerx/charts/venus-app-custom/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-app-custom
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-custom-0.1.0
    app.kubernetes.io/name: venus-app-custom
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-app-custom
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-app-custom-0.1.0
        app.kubernetes.io/name: venus-app-custom
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
      - command:
        - sh
        - -c
        - chown 1000:1000 /data/app/logs/venus-custom
        image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
        imagePullPolicy: IfNotPresent
        name: init-dir
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - name: logs
          mountPath: /data/app/logs/venus-custom
      containers:
        - name: venus-app-custom
          securityContext:
            null
          image: /onebank/venus-app-custom:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-custom
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-custom
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-app-custom-configmap
---
# Source: tellerx/charts/venus-app-depn/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-app-depn
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-depn-0.1.0
    app.kubernetes.io/name: venus-app-depn
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.1.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-app-depn
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-app-depn-0.1.0
        app.kubernetes.io/name: venus-app-depn
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.1.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-depn
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-depn
      containers:
        - name: venus-app-depn
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-app-depn:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-depn
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-depn
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-app-depn-configmap
---
# Source: tellerx/charts/venus-app-pmw/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-app-pmw
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-pmw-0.1.0
    app.kubernetes.io/name: venus-app-pmw
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "2.1.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-app-pmw
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-app-pmw-0.1.0
        app.kubernetes.io/name: venus-app-pmw
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "2.1.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-pmw
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-pmw
      containers:
        - name: venus-app-pmw
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-app-pmw:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-pmw
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-pmw
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-app-pmw-configmap
---
# Source: tellerx/charts/venus-app-sysparam/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-app-sysparam
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-sysparam-0.1.0
    app.kubernetes.io/name: venus-app-sysparam
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.4.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-app-sysparam
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-app-sysparam-0.1.0
        app.kubernetes.io/name: venus-app-sysparam
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.4.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-sysparam
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-sysparam
      containers:
        - name: venus-app-sysparam
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-app-sysparam:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-sysparam
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-sysparam
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-app-sysparam-configmap
---
# Source: tellerx/charts/venus-app-user/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-app-user
  namespace: onebank
  labels:
    helm.sh/chart: venus-app-user-0.1.0
    app.kubernetes.io/name: venus-app-user
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.3.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-app-user
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-app-user-0.1.0
        app.kubernetes.io/name: venus-app-user
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.3.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-usermanager
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-usermanager
      containers:
        - name: venus-app-user
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-app-user:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-usermanager
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-usermanager
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-app-user-configmap
---
# Source: tellerx/charts/venus-core-configservice/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-core-configservice
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-configservice-0.1.0
    app.kubernetes.io/name: venus-core-configservice
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.2.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-core-configservice
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-core-configservice-0.1.0
        app.kubernetes.io/name: venus-core-configservice
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.2.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-config
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-config
      containers:
        - name: venus-core-configservice
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-core-configservice:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-config
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-config
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-core-configservice-configmap
---
# Source: tellerx/charts/venus-core-environment/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-core-environment
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-environment-0.1.0
    app.kubernetes.io/name: venus-core-environment
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.4.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-core-environment
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-core-environment-0.1.0
        app.kubernetes.io/name: venus-core-environment
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.4.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-environ
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-environ
      containers:
        - name: venus-core-environment
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-core-environment:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-environ
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-environ
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-core-environment
---
# Source: tellerx/charts/venus-core-gateway/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-core-gateway
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-gateway-0.1.0
    app.kubernetes.io/name: venus-core-gateway
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.1.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-core-gateway
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-core-gateway-0.1.0
        app.kubernetes.io/name: venus-core-gateway
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.1.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-gateway
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-gateway
      containers:
        - name: venus-core-gateway
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-core-gateway:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 7073
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 512Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-gateway
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-gateway
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-core-gateway
---
# Source: tellerx/charts/venus-core-journal/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-core-journal
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-journal-0.1.0
    app.kubernetes.io/name: venus-core-journal
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.3.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-core-journal
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-core-journal-0.1.0
        app.kubernetes.io/name: venus-core-journal
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.3.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-journal
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-journal
      containers:
        - name: venus-core-journal
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-core-journal:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-journal
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-journal
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-core-journal
---
# Source: tellerx/charts/venus-core-scenecontrol/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-core-scenecontrol
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-scenecontrol-0.1.0
    app.kubernetes.io/name: venus-core-scenecontrol
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.5.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-core-scenecontrol
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-core-scenecontrol-0.1.0
        app.kubernetes.io/name: venus-core-scenecontrol
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.5.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-scenecontrl
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-scenecontrl
      containers:
        - name: venus-core-scenecontrol
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-core-scenecontrol:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-scenecontrl
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-scenecontrl
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-core-scenecontrol-configmap
---
# Source: tellerx/charts/venus-core-taskcontrol/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tellerx-venus-core-taskcontrol
  namespace: onebank
  labels:
    helm.sh/chart: venus-core-taskcontrol-0.1.0
    app.kubernetes.io/name: venus-core-taskcontrol
    app.kubernetes.io/instance: tellerx
    app.kubernetes.io/version: "1.2.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: venus-core-taskcontrol
      app.kubernetes.io/instance: tellerx
  template:
    metadata:
      labels:
        helm.sh/chart: venus-core-taskcontrol-0.1.0
        app.kubernetes.io/name: venus-core-taskcontrol
        app.kubernetes.io/instance: tellerx
        app.kubernetes.io/version: "1.2.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        null
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 /data/app/logs/venus-taskcontrol
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-taskcontrol
      containers:
        - name: venus-core-taskcontrol
          securityContext:
            null
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/venus-core-taskcontrol:0.0.1-alpha
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: logs
              mountPath: /data/app/logs/venus-taskcontrol
            - name: conf
              mountPath: /data/app/config/application.yml
              subPath: application.yml
      volumes:
        - name: logs
          hostPath:
            path: /home/<USER>/logs/venus-taskcontrol
            type: DirectoryOrCreate
        - name: conf
          configMap:
            name: tellerx-venus-core-taskcontrol-configmap
