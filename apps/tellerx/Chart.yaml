apiVersion: v2
name: tellerx
description: A Helm chart for TellerX platform services
type: application
version: 0.1.0
appVersion: "0.0.1"

dependencies:
  - name: operationsm-receipt
    version: 0.1.0
    repository: "file://charts/operationsm-receipt"
    condition: operationsm-receipt.enabled
  - name: venus-app-auth
    version: 0.1.0
    repository: "file://charts/venus-app-auth"
    condition: venus-app-auth.enabled
  - name: venus-app-custom
    version: 0.1.0
    repository: "file://charts/venus-app-custom"
    condition: venus-app-custom.enabled
  - name: venus-app-depn
    version: 0.1.0
    repository: "file://charts/venus-app-depn"
    condition: venus-app-depn.enabled
  - name: venus-app-pmw
    version: 0.1.0
    repository: "file://charts/venus-app-pmw"
    condition: venus-app-pmw.enabled
  - name: venus-app-sysparam
    version: 0.1.0
    repository: "file://charts/venus-app-sysparam"
    condition: venus-app-sysparam.enabled
  - name: venus-app-user
    version: 0.1.0
    repository: "file://charts/venus-app-user"
    condition: venus-app-user.enabled
  - name: venus-core-configservice
    version: 0.1.0
    repository: "file://charts/venus-core-configservice"
    condition: venus-core-configservice.enabled
  - name: venus-core-environment
    version: 0.1.0
    repository: "file://charts/venus-core-environment"
    condition: venus-core-environment.enabled
  - name: venus-core-gateway
    version: 0.1.0
    repository: "file://charts/venus-core-gateway"
    condition: venus-core-gateway.enabled
  - name: venus-core-journal
    version: 0.1.0
    repository: "file://charts/venus-core-journal"
    condition: venus-core-journal.enabled
  - name: venus-core-scenecontrol
    version: 0.1.0
    repository: "file://charts/venus-core-scenecontrol"
    condition: venus-core-scenecontrol.enabled
  - name: venus-core-taskcontrol
    version: 0.1.0
    repository: "file://charts/venus-core-taskcontrol"
    condition: venus-core-taskcontrol.enabled