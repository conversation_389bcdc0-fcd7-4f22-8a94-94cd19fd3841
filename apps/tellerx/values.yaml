global:
  namespace: "onebank"

  # podSecurityContext:
  #   runAsNonRoot: true
  #   runAsUser: 1000
  #   runAsGroup: 1000
  #   fsGroup: 1000

  # securityContext:
  #   runAsNonRoot: true
  #   runAsUser: 1000
  #   runAsGroup: 1000

  image:
    host: "************.dkr.ecr.ap-southeast-1.amazonaws.com"
    pullPolicy: "IfNotPresent"
    
  initImage:
      repository: "{{ .Values.global.image.host }}/onebank/busybox"
      tag: "latest"
      pullPolicy: "IfNotPresent"

  redis:
    database: 0
    host: "redis-standalone-redis.paas.svc.cluster.local"
    port: 6379
    password: ""
    lettuce:
      pool:
        maxActive: 20
        maxWait: 3000
        maxIdle: 5
        minIdle: 0
    timeout: 15000

  venus:
    digitalsignature: true
    mq:
      enable: false
      type: rabbitmq
      producer:
        namesrvAddr: localhost:5672
        userName: "guest"
        password: "guest"
      consumer:
        namesrvAddr: localhost:5672
        userName: "guest"
        password: "guest"

  eureka:
    client:
      enabled: false
      registerWithEureka: true
      fetchRegistry: true
      serviceUrl:
        defaultZone: "http://localhost:7071/eureka/"
      healthcheck:
        enabled: false

  nacos:
    serverAddr: "nacos-standalone-nacos.paas.cluster.svc.local:8848"

operationsm-receipt:
  enabled: true
venus-app-auth:
  enabled: false
venus-app-custom:
  enabled: false
venus-app-depn:
  enabled: false
venus-app-pmw:
  enabled: false
venus-app-sysparam:
  enabled: false
venus-app-user:
  enabled: false
venus-core-configservice:
  enabled: false
venus-core-environment:
  enabled: false
venus-core-gateway:
  enabled: false
venus-core-journal:
  enabled: false
venus-core-scenecontrol:
  enabled: false
venus-core-taskcontrol:
  enabled: false