applicationName: venus-gateway

# profile: "fat"

replicaCount: 1

# debug:
#   port: 5005

image:
  repository: "{{ .Values.global.image.host }}/onebank/{{ .Chart.Name }}"
  tag: "0.0.2-alpha"

service:
  port: 7073
  type: "ClusterIP"
  # ipAddress: localhost

# livenessProbe:
#   httpGet:
#     path: "/health"
#     port: 7073
#   initialDelaySeconds: 30
#   periodSeconds: 10

# readinessProbe:
#   httpGet:
#     path: "/ready"
#     port: 7073
#   initialDelaySeconds: 5
#   periodSeconds: 5

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

# volumeMounts:
#   - name: logs
#     mountPath: /data/app/logs

# volumes:
#   - name: logs
#     hostPath:
#       path: /home/<USER>/logs/{{ .Values.applicationName }}
#       type: DirectoryOrCreate

log:
  mountPath: /data/app/logs/{{ .Values.applicationName }}
  hostPath: /home/<USER>/logs/{{ .Values.applicationName }}

conf:
  mountPath: /data/app/config/application.yml
  subPath: application.yml

autoscaling:
  enabled: false

ingress:
  enabled: false
  hosts: []

redis:
  timeout: 60s
  jedis:
    pool:
      maxIdle: 500
      minIdle: 50
      maxWait: -1s
      maxActive: -1

configMap:
  enabled: true
  data:
    application.yml: |
      server:
        port: {{ .Values.service.port }}
        tomcat: 
          accept-count: 5000 
          max-threads: 5000
          max-connections: 20000
        ssl:
          key-store: classpath:venustest.p12
          key-store-password: 123456
          key-store-type: PKCS12
          keyAlias: server
          enabled: false

      #心跳检查开关
      eureka:
        client:
          healthcheck:
            enabled: {{ .Values.global.eureka.client.healthcheck.enabled }}

      spring:
        redis:
          ## Redis数据库索引（默认为0）
          database: {{ .Values.global.redis.database }}
          ## Redis服务器地址
          host: {{ .Values.global.redis.host }}
          ## Redis服务器连接端口
          port: {{ .Values.global.redis.port }}
          timeout: {{ .Values.redis.timeout }}  # 数据库连接超时时间，2.0 中该参数的类型为Duration，这里在配置的时候需要指明单位
          # 连接池配置，2.0中直接使用jedis或者lettuce配置连接池
          jedis:
            pool:
              # 最大空闲连接数
              max-idle: {{ .Values.redis.jedis.pool.maxIdle }}
              # 最小空闲连接数
              min-idle: {{ .Values.redis.jedis.pool.minIdle }}
              # 等待可用连接的最大时间，负数为不限制
              max-wait: {{ .Values.redis.jedis.pool.maxWait }}
              # 最大活跃连接数，负数为不限制
              max-active: {{ .Values.redis.jedis.pool.maxActive }}

      venus:
        digitalsignature: {{ .Values.global.venus.digitalsignature }}
      
      ratelimiter-conf:
        #配置限流参数与RateLimiterConf类映射
        rateLimitMap:
          #格式为：routeid(gateway配置routes时指定的).系统名称.replenishRate(流速)/burstCapacity令牌桶大小
          app-service.TELLER_USER.replenishRate: 8000
          app-service.TELLER_USER.burstCapacity: 10000

      ribbon:
        nacos:
          enabled: false

      #健康检查
      management:
        endpoints:
          web:
            exposure:
              include: "health,info"
        server:
          servlet:
            context-path: /
          ssl:
            enabled: false
        endpoint:
          health:
            show-details: always   ##never 概要  always 详细
        health:
          rabbit:
            enabled: false    #rabbit健康检查关闭
          redis:
            enabled: false