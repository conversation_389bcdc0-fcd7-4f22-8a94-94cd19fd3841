apiVersion: v1
kind: Service
metadata:
  name: {{ include "venus-app-user.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "venus-app-user.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "venus-app-user.selectorLabels" . | nindent 4 }}