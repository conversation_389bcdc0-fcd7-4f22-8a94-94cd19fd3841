applicationName: operationsm-receipt

profile: "fat"

replicaCount: 1

debug:
  port: 5005

image:
  repository: "{{ .Values.global.image.host }}/onebank/{{ .Chart.Name }}"
  tag: "0.0.19-alpha"

service:
  port: 8080
  type: "ClusterIP"
  ipAddress: localhost

# livenessProbe:
#   httpGet:
#     path: "/health"
#     port: 8080
#   initialDelaySeconds: 30
#   periodSeconds: 10

# readinessProbe:
#   httpGet:
#     path: "/ready"
#     port: 8080
#   initialDelaySeconds: 5
#   periodSeconds: 5

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# volumeMounts:
#   - name: logs
#     mountPath: /data/app/logs

# volumes:
#   - name: logs
#     hostPath:
#       path: /home/<USER>/logs/{{ .Values.applicationName }}
#       type: DirectoryOrCreate

log:
  mountPath: /data/app/logs/{{ .Values.applicationName }}
  hostPath: /home/<USER>/logs/{{ .Values.applicationName }}

conf:
  mountPath: /data/app/config/application.yml
  subPath: application.yml

autoscaling:
  enabled: false

ingress:
  enabled: false
  hosts: []

configMap:
  enabled: true
  data:
    application.yml: |
      server:
        port: {{ .Values.service.port }}

      # spring:
      #   cache:
      #     type: redis
      #   #redis配置
      #   redis:
      #     ## Redis数据库索引（默认为0）
      #     database: {{ .Values.global.redis.database }}
      #     ## Redis服务器地址
      #     host: {{ .Values.global.redis.host }}
      #     ## Redis服务器连接端口
      #     port: {{ .Values.global.redis.port }}
      #     ## Redis服务器连接密码（默认为空）
      #     password: {{ .Values.global.redis.password }}
      #     lettuce:
      #       pool:
      #         ## 连接池最大连接数（使用负值表示没有限制）
      #         #spring.redis.pool.max-active=8
      #         max-active: {{ .Values.global.redis.lettuce.pool.maxActive }}
      #         ## 连接池最大阻塞等待时间（使用负值表示没有限制）
      #         #spring.redis.pool.max-wait=-1
      #         max-wait: {{ .Values.global.redis.lettuce.pool.maxWait }}
      #         ## 连接池中的最大空闲连接
      #         #spring.redis.pool.max-idle=8
      #         max-idle: {{ .Values.global.redis.lettuce.pool.maxIdle }}
      #         ## 连接池中的最小空闲连接
      #         #spring.redis.pool.min-idle=0
      #         min-idle: {{ .Values.global.redis.lettuce.pool.minIdle }}
      #     ## 连接超时时间（毫秒）
      #     timeout: {{ .Values.global.redis.lettuce.timeout }}

      venus:
        digitalsignature: {{ .Values.global.venus.digitalsignature }}
        mq:
          enable: {{ .Values.global.venus.mq.enable }}  #消息中间件是否启动
          type: {{ .Values.global.venus.mq.type }}      #消息中间件的类型rocketmq、rabbitmq、sofamq
          producer:
            groupName: P_teller_venus-paperless   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
            namesrvAddr: {{ .Values.global.venus.mq.producer.namesrvAddr }}
            userName: {{ .Values.global.venus.mq.producer.userName }}
            password: {{ .Values.global.venus.mq.producer.password }}
          consumer:
            groupName: S_teller_venus-paperless  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
            namesrvAddr: {{ .Values.global.venus.mq.consumer.namesrvAddr }}
            userName: {{ .Values.global.venus.mq.consumer.userName }}
            password: {{ .Values.global.venus.mq.consumer.password }}
            qindex: centralauth

      #心跳检查开关
      eureka:
        client:
          healthcheck:
            enabled: {{ .Values.global.eureka.client.healthcheck.enabled }}
      
      #健康检查
      management:
        endpoints:
          web:
            exposure:
              include: "health,info"
        server:
          servlet:
            context-path: /
          ssl:
            enabled: false
        endpoint:
          health:
            show-details: always   ##never 概要  always 详细
        health:
          rabbit:
            enabled: false    #rabbit健康检查关闭

    # bootstrap.yml: |
    #   servicename: {{ .Chart.Name }}

    #   eureka:
    #     client:
    #       enabled: {{ .Values.global.eureka.client.enabled }}
    #       register-with-eureka: {{ .Values.global.eureka.client.registerWithEureka }}
    #       fetch-registry: {{ .Values.global.eureka.client.fetchRegistry }}
    #       serviceUrl:
    #         defaultZone: {{ .Values.global.eureka.client.serviceUrl.defaultZone }}
    #       healthcheck:
    #         enabled: {{ .Values.global.eureka.client.healthcheck.enabled }}   #心跳检查开关
    #     instance:
    #       instance-id: {{ .Values.service.ipAddress }}:{{ .Chart.Name }}:8811
    #       preferIpAddress: true

    #   spring:
    #     main:
    #       allow-bean-definition-overriding: true
    #     application:
    #       name: {{ .Values.applicationName }}
    #     cloud:
    #       config:
    #         enabled: false
    #         name: start-config
    #         profile: {{ .Values.profile }}
    #         label: master
    #         discovery:
    #           enabled: false  #开启eureka需要改成 true
    #           service-id: venus-config
    #       nacos:
    #         server-addr: {{ .Values.global.nacos.serverAddr }}
    #         discovery:
    #           enabled:  true
    #         config:
    #           enabled:  true     #为true表示使用nacos配置中心，使用euraka注册中心时改为false
    #           file-extension: properties
    #           refresh-enabled: true
    #           extension-configs:
    #             - data-id: venusconfig.properties
    #               refresh: true
                            
    #   logging:
    #     config: classpath:logging-config-receipt.xml

    #   debugport: {{ .Values.debug.port }}

    # logging-config-receipt.xml: |
    #   <?xml version="1.0" encoding="UTF-8"?>
    #   <configuration>
    #     <!-- 使用默认配置的部分值 -->
    #     <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    #     <springProperty scope="context" name="springAppName"
    #       source="spring.application.name" />
    #     <!-- 从LibEnvironmentStart获取的随机数字 这个数字被用于微服务内置web容器的端口号-->
    #     <springProperty scope="context" name="springAppPort"
    #       source="springAppPort" />
    #     <!-- 定义日志输出格式 -->
    #     <property name="logging.pattern"
    #           value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{serviceCode}] [%X{transId}] [%logger] [%t][%5p] %m%n"/>
    #     <!--输出到控制台-->
    #     <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
    #       <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
    #         <level>DEBUG</level>
    #       </filter>
    #       <encoder>
    #         <!--格式化输出：%d:表示日期    %thread:表示线程名     %-5level:级别从左显示5个字符宽度  %msg:日志消息    %n:是换行符-->
    #         <pattern>%black(控制台-) %red(%d{yyyy-MM-dd HH:mm:ss}) %green([%X{transId}]) %highlight(%-5level) %boldMagenta(%logger) - %cyan(%msg%n)
    #         </pattern>
    #         <charset>UTF-8</charset>
    #       </encoder>
    #     </appender>

    #     <appender name="fileAppender"
    #       class="ch.qos.logback.core.rolling.RollingFileAppender">
    #       <File>logs/{{ .Chart.Name }}/sys.{{ .Chart.Name }}.log</File>
    #       <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
    #         <!-- 活动文件的名字会根据fileNamePattern的值，每隔一段时间改变一次 -->
    #         <!-- 文件名：log/sys.2018-03-05.0.log -->
    #   <!--		<fileNamePattern>/app/qhdprod/logs/{{ .Chart.Name }}/%d{yyyyMMdd}/sys.{{ .Chart.Name }}.%d.%i.log</fileNamePattern>-->
    #         <fileNamePattern>logs/{{ .Chart.Name }}/sys.{{ .Chart.Name }}.%d.%i.log</fileNamePattern>
    #         <!-- 每产生一个日志文件，该日志文件的保存期限为90天 -->
    #         <maxHistory>30</maxHistory>
    #         <timeBasedFileNamingAndTriggeringPolicy
    #           class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
    #           <!-- maxFileSize:这是活动文件的大小，默认值是10MB -->
    #           <maxFileSize>10MB</maxFileSize>
    #         </timeBasedFileNamingAndTriggeringPolicy>
    #       </rollingPolicy>
    #       <encoder><!-- 必须指定，否则不会往文件输出内容 -->
    #         <pattern>
    #           %d{yyyy-MM-dd HH:mm:ss.SSS} [%X{serviceCode}] [%X{transId}] [%logger] [%t][%5p] %m%n
    #         </pattern>
    #         <charset>UTF-8</charset>
    #       </encoder>
    #       <append>true</append>
    #       <prudent>false</prudent>
    #     </appender>

    #     <root level="INFO">
    #       <appender-ref ref="console" />
    #       <appender-ref ref="fileAppender" />
    #     </root>
    #   <!--	&lt;!&ndash; 服务入口 &ndash;&gt;-->
    #   <!--	<logger name="org.springframework.web.servlet"  additivity="false" level="INFO">-->
    #   <!--		<appender-ref ref="fileAppender" />-->
    #   <!--	</logger>-->
    #   <!--	<logger name="com.dcits.operationsm.start"  additivity="false" level="INFO">-->
    #   <!--		<appender-ref ref="fileAppender" />-->
    #   <!--	</logger>-->
    #   <!--	&lt;!&ndash; 数据库操作 &ndash;&gt;-->
    #   <!--	<logger name="com.dcfs.venus.datastorage"  additivity="false" level="INFO">-->
    #   <!--		<appender-ref ref="fileAppender" />-->
    #   <!--	</logger>-->
    #   <!--	&lt;!&ndash; 服务代理 &ndash;&gt;-->
    #   <!--	<logger name="com.dcits.venus.serviceproxy"  additivity="false" level="INFO">-->
    #   <!--		<appender-ref ref="fileAppender" />-->
    #   <!--	</logger>-->
    #   <!--	&lt;!&ndash; 原子服务操作 &ndash;&gt;-->
    #   <!--	<logger name="com.dcits.venus.atomservice"  additivity="false" level="INFO">-->
    #   <!--		<appender-ref ref="fileAppender" />-->
    #   <!--	</logger>-->
    #   <!--	<logger name="com.dcfs.venus.app"  additivity="false" level="INFO">-->
    #   <!--		<appender-ref ref="fileAppender" />-->
    #   <!--	</logger>-->
    #   <!--	<logger name="org.springframework.amqp.rabbit.listener"  additivity="false" level="INFO">-->
    #   <!--		<appender-ref ref="fileAppender" />-->
    #   <!--	</logger>-->

    #     <logger name="org.springframework.web.servlet"  additivity="false" level="DEBUG">
    #       <appender-ref ref="console" />
    #     </logger>
    #     <logger name="com.dcits.operationsm.start"  additivity="false" level="DEBUG">
    #       <appender-ref ref="console" />
    #     </logger>
    #     <!-- 数据库操作 -->
    #     <logger name="com.dcfs.venus.datastorage"  additivity="false" level="INFO">
    #       <appender-ref ref="console" />
    #     </logger>
    #     <!-- 服务代理 -->
    #     <logger name="com.dcits.venus.serviceproxy"  additivity="false" level="DEBUG">
    #       <appender-ref ref="console" />
    #     </logger>
    #     <!-- 原子服务操作 -->
    #     <logger name="com.dcits.venus.atomservice"  additivity="false" level="DEBUG">
    #       <appender-ref ref="console" />
    #     </logger>
    #     <logger name="com.dcfs.venus.app"  additivity="false" level="DEBUG">
    #       <appender-ref ref="console" />
    #     </logger>
    #     <logger name="org.springframework.amqp.rabbit.listener"  additivity="false" level="DEBUG">
    #       <appender-ref ref="console" />
    #     </logger>
    #   </configuration>
