apiVersion: v1
kind: Service
metadata:
  name: {{ include "operationsm-receipt.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "operationsm-receipt.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "operationsm-receipt.selectorLabels" . | nindent 4 }}