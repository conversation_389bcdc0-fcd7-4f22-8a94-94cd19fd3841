{{- if .Values.configMap.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "operationsm-receipt.fullname" . }}-configmap
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    app.kubernetes.io/name: {{ include "operationsm-receipt.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: operationsm-receipt
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  {{- range $key, $value := .Values.configMap.data }}
  {{ $key }}: |
    {{- tpl $value $ | nindent 4 }}
  {{- end }}
{{- end }} 