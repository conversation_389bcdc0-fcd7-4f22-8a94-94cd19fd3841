applicationName: venus-scenecontrl

# profile: "fat"

replicaCount: 1

# debug:
#   port: 5005

image:
  repository: "{{ .Values.global.image.host }}/onebank/{{ .Chart.Name }}"
  tag: "0.0.2-alpha"

service:
  port: 8080
  type: "ClusterIP"
  # ipAddress: localhost

# livenessProbe:
#   httpGet:
#     path: "/health"
#     port: 8080
#   initialDelaySeconds: 30
#   periodSeconds: 10

# readinessProbe:
#   httpGet:
#     path: "/ready"
#     port: 8080
#   initialDelaySeconds: 5
#   periodSeconds: 5

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# volumeMounts:
#   - name: logs
#     mountPath: /data/app/logs

# volumes:
#   - name: logs
#     hostPath:
#       path: /home/<USER>/logs/{{ .Values.applicationName }}
#       type: DirectoryOrCreate

log:
  mountPath: /data/app/logs/{{ .Values.applicationName }}
  hostPath: /home/<USER>/logs/{{ .Values.applicationName }}

conf:
  mountPath: /data/app/config/application.yml
  subPath: application.yml

autoscaling:
  enabled: false

ingress:
  enabled: false
  hosts: []

configMap:
  enabled: true
  data:
    application.yml: |
      server:
        port: {{ .Values.service.port }}

      spring:
        cache:
          type: redis
        #redis配置
        redis:
          ## Redis数据库索引（默认为0）
          database: {{ .Values.global.redis.database }}
          ## Redis服务器地址
          host: {{ .Values.global.redis.host }}
          ## Redis服务器连接端口
          port: {{ .Values.global.redis.port }}
          ## Redis服务器连接密码（默认为空）
          password: {{ .Values.global.redis.password }}
          lettuce:
            pool:
              ## 连接池最大连接数（使用负值表示没有限制）
              #spring.redis.pool.max-active=8
              max-active: {{ .Values.global.redis.lettuce.pool.maxActive }}
              ## 连接池最大阻塞等待时间（使用负值表示没有限制）
              #spring.redis.pool.max-wait=-1
              max-wait: {{ .Values.global.redis.lettuce.pool.maxWait }}
              ## 连接池中的最大空闲连接
              #spring.redis.pool.max-idle=8
              max-idle: {{ .Values.global.redis.lettuce.pool.maxIdle }}
              ## 连接池中的最小空闲连接
              #spring.redis.pool.min-idle=0
              min-idle: {{ .Values.global.redis.lettuce.pool.minIdle }}
          ## 连接超时时间（毫秒）
          timeout: {{ .Values.global.redis.lettuce.timeout }}

      venus:
        digitalsignature: {{ .Values.global.venus.digitalsignature }}
        mq:
          enable: {{ .Values.global.venus.mq.enable }}  #消息中间件是否启动
          type: {{ .Values.global.venus.mq.type }}      #消息中间件的类型rocketmq、rabbitmq、sofamq
          producer:
            groupName: P_teller_scenecrl   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
            namesrvAddr: {{ .Values.global.venus.mq.producer.namesrvAddr }}
            userName: {{ .Values.global.venus.mq.producer.userName }}
            password: {{ .Values.global.venus.mq.producer.password }}
          consumer:
            groupName: S_teller_scenecrl  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
            namesrvAddr: {{ .Values.global.venus.mq.consumer.namesrvAddr }}
            userName: {{ .Values.global.venus.mq.consumer.userName }}
            password: {{ .Values.global.venus.mq.consumer.password }}
            qindex: scenecrl

      feign:
        client:
          config:
            default:
              connect-timeout: 60000
              read-timeout: 60000
        httpclient:
          connection-timeout: 60000
          max-connections: 500
        hystrix:
          enabled: false

      #断路器配置
      hystrix:
        metrics:
          enabled: true
          #刷新间隔
          polling-interval-ms: 2000

      #健康检查
      management:
        endpoints:
          web:
            exposure:
              include: "health,info"
        server:
          servlet:
            context-path: /
          ssl:
            enabled: false
        endpoint:
          health:
            show-details: always   ##never 概要  always 详细
        health:
          rabbit:
            enabled: false    #rabbit健康检查关闭

      #编译器选择
        customparam:
          viewcompiler: "vue"   #vue or react
