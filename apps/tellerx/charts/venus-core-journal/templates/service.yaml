apiVersion: v1
kind: Service
metadata:
  name: {{ include "venus-core-journal.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "venus-core-journal.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "venus-core-journal.selectorLabels" . | nindent 4 }}