applicationName: venus-journal

# profile: "fat"

replicaCount: 1

# debug:
#   port: 5005

image:
  repository: "{{ .Values.global.image.host }}/onebank/{{ .Chart.Name }}"
  tag: "0.0.2-alpha"

service:
  port: 8080
  type: "ClusterIP"
  # ipAddress: localhost

# livenessProbe:
#   httpGet:
#     path: "/health"
#     port: 8080
#   initialDelaySeconds: 30
#   periodSeconds: 10

# readinessProbe:
#   httpGet:
#     path: "/ready"
#     port: 8080
#   initialDelaySeconds: 5
#   periodSeconds: 5

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# volumeMounts:
#   - name: logs
#     mountPath: /data/app/logs

# volumes:
#   - name: logs
#     hostPath:
#       path: /home/<USER>/logs/{{ .Values.applicationName }}
#       type: DirectoryOrCreate

log:
  mountPath: /data/app/logs/{{ .Values.applicationName }}
  hostPath: /home/<USER>/logs/{{ .Values.applicationName }}

conf:
  mountPath: /data/app/config/application.yml
  subPath: application.yml

autoscaling:
  enabled: false

ingress:
  enabled: false
  hosts: []

configMap:
  enabled: true
  data:
    application.yml: |
      server:
        port: {{ .Values.service.port }}

      spring:
        http: 
          encoding:
            charset: UTF-8
            force: true
            enabled: true
        servlet:
          multipart:
            max-file-size: 50MB
            max-request-size: 50MB

      venus:
        journal-thred-pool: 200
        digitalsignature: {{ .Values.global.venus.digitalsignature }}
        mq:
          enable: {{ .Values.global.venus.mq.enable }}  #消息中间件是否启动
          type: {{ .Values.global.venus.mq.type }}      #消息中间件的类型rocketmq、rabbitmq、sofamq
          producer:
            groupName: P_teller_journal   #命名规范P_应用名_服务名，不能重名  sofa/rocketmp需配置
            namesrvAddr: {{ .Values.global.venus.mq.producer.namesrvAddr }}
            userName: {{ .Values.global.venus.mq.producer.userName }}
            password: {{ .Values.global.venus.mq.producer.password }}
          consumer:
            groupName: S_teller_journal  #命名规范S_应用名_服务名，不能重名  sofa/rocketmp需配置
            namesrvAddr: {{ .Values.global.venus.mq.consumer.namesrvAddr }}
            userName: {{ .Values.global.venus.mq.consumer.userName }}
            password: {{ .Values.global.venus.mq.consumer.password }}
            qindex: journal

      feign:
        client:
          config:
            venus-cms:
              connect-timeout: 60000
              read-timeout: 60000           
        httpclient:
          connection-timeout: 60000
          max-connections: 500
        hystrix:
          enabled: false

      #健康检查
      management:
        endpoints:
          web:
            exposure:
              include: "health,info"
        server:
          servlet:
            context-path: /
          ssl:
            enabled: false
        endpoint:
          health:
            show-details: always   ##never 概要  always 详细
