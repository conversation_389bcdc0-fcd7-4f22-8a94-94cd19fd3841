apiVersion: v1
kind: Service
metadata:
  name: {{ include "venus-core-taskcontrol.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "venus-core-taskcontrol.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "venus-core-taskcontrol.selectorLabels" . | nindent 4 }}