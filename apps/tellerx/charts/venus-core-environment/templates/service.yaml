apiVersion: v1
kind: Service
metadata:
  name: {{ include "venus-core-environment.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "venus-core-environment.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "venus-core-environment.selectorLabels" . | nindent 4 }}