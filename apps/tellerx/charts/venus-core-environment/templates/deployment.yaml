apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "venus-core-environment.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "venus-core-environment.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount | default 1 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "venus-core-environment.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "venus-core-environment.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.global.podSecurityContext | nindent 8 }}
      initContainers:
        - command:
          - sh
          - -c
          - chown 1000:1000 {{ tpl .Values.log.mountPath . }}
          image: {{ tpl .Values.global.initImage.repository . }}:{{ .Values.global.initImage.tag }}
          imagePullPolicy: {{ .Values.global.initImage.pullPolicy }}
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: logs
              mountPath: {{ tpl .Values.log.mountPath . }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.global.securityContext | nindent 12 }}
          image: {{ tpl .Values.image.repository . }}:{{ .Values.image.tag | default .Chart.AppVersion }}
          imagePullPolicy: {{ .Values.image.pullPolicy | default .Values.global.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          {{- with .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: logs
              mountPath: {{ tpl .Values.log.mountPath . }}
          {{- if .Values.configMap.enabled }}
            - name: conf
              mountPath: {{ .Values.conf.mountPath }}
            {{- with .Values.conf.subPath }}
              subPath: {{ . }}
            {{- end }}
          {{- end }}
          {{- with .Values.volumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
      volumes:
        - name: logs
          hostPath:
            path: {{ tpl .Values.log.hostPath . }}
            type: DirectoryOrCreate
        {{- if .Values.configMap.enabled }}
        - name: conf
          configMap:
            name: {{ include "venus-core-environment.fullname" . }}
        {{- end }}
        {{- with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}