applicationName: venus-config

# profile: "fat"

replicaCount: 1

# debug:
#   port: 5005

image:
  repository: "{{ .Values.global.image.host }}/onebank/{{ .Chart.Name }}"
  tag: "0.0.2-alpha"

service:
  port: 8080
  type: "ClusterIP"
  # ipAddress: localhost

# livenessProbe:
#   httpGet:
#     path: "/health"
#     port: 8080
#   initialDelaySeconds: 30
#   periodSeconds: 10

# readinessProbe:
#   httpGet:
#     path: "/ready"
#     port: 8080
#   initialDelaySeconds: 5
#   periodSeconds: 5

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# volumeMounts:
#   - name: logs
#     mountPath: /data/app/logs

# volumes:
#   - name: logs
#     hostPath:
#       path: /home/<USER>/logs/{{ .Values.applicationName }}
#       type: DirectoryOrCreate

log:
  mountPath: /data/app/logs/{{ .Values.applicationName }}
  hostPath: /home/<USER>/logs/{{ .Values.applicationName }}

conf:
  mountPath: /data/app/config/application.yml
  subPath: application.yml

autoscaling:
  enabled: false

ingress:
  enabled: false
  hosts: []

configMap:
  enabled: true
  data:
    application.yml: |
      server:
        port: {{ .Values.service.port }}

      #健康检查
      management:
        endpoints:
          web:
            exposure:
              include: "health,info"
        server:
          servlet:
            context-path: /
          ssl:
            enabled: false
        endpoint:
          health:
            show-details: always   ##never 概要  always 详细
