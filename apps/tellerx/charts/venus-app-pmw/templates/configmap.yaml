{{- if .Values.configMap.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "venus-app-pmw.fullname" . }}-configmap
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    app.kubernetes.io/name: {{ include "venus-app-pmw.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: venus-app-pmw
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  {{- range $key, $value := .Values.configMap.data }}
  {{ $key }}: |
    {{- tpl $value $ | nindent 4 }}
  {{- end }}
{{- end }}
