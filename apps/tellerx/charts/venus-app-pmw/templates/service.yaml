apiVersion: v1
kind: Service
metadata:
  name: {{ include "venus-app-pmw.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "venus-app-pmw.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "venus-app-pmw.selectorLabels" . | nindent 4 }}