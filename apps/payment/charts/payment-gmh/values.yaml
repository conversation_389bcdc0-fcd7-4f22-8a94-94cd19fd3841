ingress:
  enabled: false
  className: ""
  annotations:
    {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
replicas: 2
# HPA configuration
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
# image configuration
initImage:
  repository: "{{ .Values.global.imageHost }}/onebank/busybox"
  tag: "latest"
  pullPolicy: "IfNotPresent"
image:
  repository: "{{ .Values.global.imageHost }}/onebank/payment-gmh"
  tag: "0.0.9-alpha"
  pullPolicy: "IfNotPresent"
# config directory configuration
configDirectory: "/app/config/config/application-fat.yml"
# log directory configuration
log:
  directory: "/logs/payment-gmh"
  volumeName: "log-volume"
  hostDirectory: "/home/<USER>/logs/payment-gmh"

# Service configuration
service:
  type: "ClusterIP"
  port: 8080
  targetPort: 8080

# Resource configuration
resources:
  limits:
    cpu: "500m"
    memory: "512Mi"
  requests:
    cpu: "50m"
    memory: "64Mi"

# Additional volumes
volumeMounts:
  - name: "configuration-volume"
    mountPath: '{{ .Values.configDirectory | default "/data/app/config/application-fat.yml" }}'
    subPath: "application-fat.yml"
volumes:
  - name: "configuration-volume"
    configMap:
      name: "payment-payment-gmh-configmap"

# ConfigMap configuration
configMap:
  enabled: true
  data:
    application-fat.yml: |
      spring:
        cloud:
          nacos:
            discovery:
              enabled: {{ .Values.global.middleware.defaultPlatform.nacos.enable }}
              server-addr: {{ include "payment-gmh.fullname" . }}-service.{{ .Values.global.namespace }}.svc.cluster.local:8848
              tenant: {{ .Values.global.middleware.defaultPlatform.nacos.tenant }}
              workspace: {{ .Values.global.middleware.defaultPlatform.nacos.workspace }}
              cluster-name: {{ .Values.global.middleware.defaultPlatform.nacos.clusterName | default "onebank" }}
              prefer-ip-address: {{ .Values.global.middleware.defaultPlatform.nacos.preferIpAddress | default false }}  # 禁用IP注册
              use-hostname: {{ .Values.global.middleware.defaultPlatform.nacos.useHostname | default true }}        # 使用K8s Service名称
        redis:
          host: {{ .Values.global.middleware.defaultPlatform.redis.host }}
          port: {{ .Values.global.middleware.defaultPlatform.redis.port }}
          password: {{ .Values.global.middleware.defaultPlatform.redis.password }}
          database: {{ .Values.global.middleware.defaultPlatform.redis.database }}
        datasource:
          username: {{ .Values.global.middleware.defaultDatabase.username }}
          password: {{ .Values.global.middleware.defaultDatabase.password }}
          url: {{ .Values.global.middleware.defaultDatabase.host }}
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            #初始化物理连接个数glCore
            initial-size: 20
            #最小连接池数量
            min-idle: 20
            #最大连接池数量
            max-active: 200
            # 配置获取连接等待超时的时间
            max-wait: 600000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            min-evictable-idle-time-millis: 300000
            #用来检测连接是否有效的sql，要求是一个查询语句,如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会其作用
            validation-query: SELECT 'X' FROM DUAL
            #申请连接的时候检测，如果空闲时间大于imeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
            test-while-idle: true
            #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            test-on-borrow: false
            #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
            test-on-return: false
            # 通过connectProperties属性来打开mergeSql功能；慢SQL记录

            # 合并多个DruidDataSource的监控数据
            use-global-data-source-stat: false
            # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
            filters: slf4j
            # 打开PSCache，并且指定每个连接上PSCache的大小
            pool-prepared-statements: true
            #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
            max-pool-prepared-statement-per-connection-size: 20
            #最小空闲连接数保活
            keep-alive: true
            #以下为基于spring boot web的内嵌druid监控，若需开启请将三个值均置为true
            stat-view-servlet:
              enabled: false
            web-stat-filter:
              enabled: false
            filter:
              stat: false
            fail-fast: true
      logging:
        #logback地址
        config: classpath:logback-spring.xml

      galaxy:
        tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId }}
        profile: {{ .Values.global.middleware.defaultGalaxy.profile }}
        #  4.0配置
        #  availableZone: dc01
        #  gateway: http://comet-gateway-service
        #  tenant: nebula
        #  version: 1.1.0
        #  workspace: system
        appId: {{ .Values.global.middleware.defaultGalaxy.appId }}
        appIdTwo: {{ .Values.global.middleware.defaultGalaxy.appIdTwo }}
        # 所属数据中心,多中心部署时配置
        dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter }}
        #单元化
        logicUnitId: {{ .Values.global.middleware.defaultGalaxy.logicUnitId }}
        phyUnitId: {{ .Values.global.middleware.defaultGalaxy.phyUnitId }}
      sonic:
        executor:
          #是否启用执行端服务,默认开启，如需关闭可设为false
          enabled: {{ .Values.global.middleware.defaultPlatform.sonic.executor.enabled }}
          clientProfile:
            machineId: {{ .Values.global.middleware.defaultPlatform.sonic.executor.machineId }}
            schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.executor.schedulerAddresses }}
            beanGeneratorType: {{ .Values.global.middleware.defaultPlatform.sonic.executor.beanGeneratorType }}
          lock:
            #是否开启执行锁，默认false
            enable: {{ .Values.global.middleware.defaultPlatform.sonic.executor.lock.enable }}
            #数据库类型 mysql,oracle,db2,sqlserver,oceanbase
            dbType: {{ .Values.global.middleware.defaultPlatform.sonic.executor.lock.dbType }}
          stepCache:
            #是否开启执行结果缓存，默认fasle
            enable: {{ .Values.global.middleware.defaultPlatform.sonic.executor.stepCache.enable }}
            #默认使用数据库缓存
            cacheType: {{ .Values.global.middleware.defaultPlatform.sonic.executor.stepCache.cacheType | default "db" }}
      sequences:
        servers: {{ .Values.global.middleware.defaultPlatform.sequences.address }} #序列服务器列表，多个使用西文逗号分割，建议将已知的所有服务节点都进行配置
        enabled: {{ .Values.global.middleware.defaultPlatform.sequences.enable }} #是否开启序列功能，缺省为true
        #loadBalance: roundRobin #路由策略
        #failplocy:failover #序列申请失败处理策略failover/failfast 默认failover
        #retry: #在failover模式下的尝试次数
        #  times: 3 #在failover模式下的尝试次数
        #  interval: 100 #重试间隔
        #message:
        #  timeout: 1000 #通讯超时时间，单次获取序列超时时间
      #数据库类型:Oracle,MySQL ...
      payment:
        databaseType: {{ .Values.global.middleware.defaultDatabase.databaseType | default "MySQL" }}
