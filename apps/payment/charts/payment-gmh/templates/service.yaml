apiVersion: v1
kind: Service
metadata:
  name: {{ include "payment-gmh.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "payment-gmh.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "payment-gmh.selectorLabels" . | nindent 4 }}
