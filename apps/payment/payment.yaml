---
# Source: payment-parent/charts/payment-gmh/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: payment-payment-gmh-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: payment-gmh
    app.kubernetes.io/instance: payment
    app.kubernetes.io/component: payment-gmh
    app.kubernetes.io/managed-by: Helm
data:
  application-fat.yml: |
    server:
      #web端口
      port: 12083
    spring:
      cloud:
        nacos:
          discovery:
            server-addr: smartgl-smartgl-batch-service.onebank.svc.cluster.local:8848
            prefer-ip-address: false  # 禁用IP注册
            use-hostname: true        # 使用K8s Service名称
      datasource:
        username: ENS_FAT
        password: ENC(7f003151f1d531102ab890b5513fb180)
        driver-class-name: oracle.jdbc.OracleDriver
        url: *******************************************
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
          #初始化物理连接个数glCore
          initial-size: 20
          #最小连接池数量
          min-idle: 20
          #最大连接池数量
          max-active: 200
          # 配置获取连接等待超时的时间
          max-wait: 600000
          # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          time-between-eviction-runs-millis: 60000
          # 配置一个连接在池中最小生存的时间，单位是毫秒
          min-evictable-idle-time-millis: 300000
          #用来检测连接是否有效的sql，要求是一个查询语句,如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会其作用
          validation-query: SELECT 'X' FROM DUAL
          #申请连接的时候检测，如果空闲时间大于imeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
          test-while-idle: true
          #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
          test-on-borrow: false
          #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
          test-on-return: false
          # 通过connectProperties属性来打开mergeSql功能；慢SQL记录

          # 合并多个DruidDataSource的监控数据
          use-global-data-source-stat: false
          # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
          filters: slf4j
          # 打开PSCache，并且指定每个连接上PSCache的大小
          pool-prepared-statements: true
          #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
          max-pool-prepared-statement-per-connection-size: 20
          #最小空闲连接数保活
          keep-alive: true
          #以下为基于spring boot web的内嵌druid监控，若需开启请将三个值均置为true
          stat-view-servlet:
            enabled: false
          web-stat-filter:
            enabled: false
          filter:
            stat: false
          fail-fast: true
    platform:
      eureka: ***************:9527
      nacos:
        enable: true
        address: **************:8848
      #kafka: dcits.cbs.kafka.sit1:9092
      orbit:
        enable: true
      jupiter:
        #服务网格开关
        mesh:
          enable: false
        #单元化开关
        unitized:
          enable: false
          #需要按单元化逻辑调用的应用
          app:
      mars: **************:9994
      gateway: dcits.cbs.gateway.sit1:8081
      sonic:
        schedulerAddresses: **************:9999
        lock:
          db-type: oracle
      mq:
        producer: **************:13070
        consumer: **************:13070
      redis:
        host: **************
        port: 6379
      apollo:
        enabled: false
        cluster: dev
        url: http://dcits.cbs.apoconf.sit1:8080

    mybatis:
      #mybatis拓展属性文件路径
      config-location: classpath:mybatis/mybatis-config-oracle.xml
      #mysql数据库驱动下，指定tdsql数据库，确保运行sql使用的是tdsql专用的方言sql
      #configurationProperties:
      #  databaseId: tdsql

    sftp:
      client:
        protocol: shareFile
        # ip地址
        host: **************
        # 端口
        port: 22
        # 用户名
        username: fat
        # 密码
        password: fat202101
        # 根路径
        root: /home/<USER>/share/
        # 本地根路径
        lroot: /home/<USER>/share/
        # 密钥文件路径
        privateKey:
        # 密钥的密码
        passphrase: 
        #se
        sessionStrictHostKeyChecking: false
        # session连接超时时间
        sessionConnectTimeout: 15000
        # channel连接超时时间
        channelConnectedTimeout: 15000
        # 大文件拆分--拆分文件存放目录
        fileSplitDir: split
        # 大文件拆分--文件大小，单位MB，超过该配置大小在和sftp.client.fileSplitPercent配置项合并计算，决定是否拆分文件
        fileSplitSize: 2
        # 大文件拆分--超出范围百分比，单位%，1-100的数，超出设定的文件大小后，超出部分超出对应百分比后才进行拆分
        fileSplitPercent: 20
        # 大文件拆分--拆分后文件记录行数
        fileSplitLines: 20000
    com:
      dcits:
        path:
          #该路径为GL本地文件存放路径，包括本地下载，及预备上传文件
          localFilePath: /home/<USER>/share/gl_file/
          #该路径为SIT环境共享文件夹，注意 当模式为sftp时远端路径不要配置第一个"/" eg: home/apps/file/ 并且路径从配置的root路径后自动拼接，当模式为shared时则必须配置 eg: /home/<USER>/file/，不受root路径配置影响
          targetFilePath: /home/<USER>/share/
          #该路径为存贷联合自动回收远端文件路径
          clTargetFilePath: /home/<USER>/file/gl_file/
          #该路径为与老核心短信通知远程路径
          clSmsFilePath: /home/<USER>/
          #用户路径
          userPath: /home/<USER>/

    #监控使用
    jupiter:
      mesh:
        enabled: false #网格调用需开启
      metrics:
        enable: true
      unitized:
        enabled: false #单元化需开启
        app:   #需要按单元化逻辑调用的应用

    management:
      endpoints:
        web:
          base-path: /actuator

    ## nacos配置
    galaxy:
      tenantId: online-batch
      profile: fat
      #  4.0配置
      #  availableZone: dc01
      #  gateway: http://comet-gateway-service
      #  tenant: nebula
      #  version: 1.1.0
      #  workspace: system
      appId: SmartGl
      appIdTwo: SMARTGL-REPORT
      # 所属数据中心,多中心部署时配置
      dataCenter: dc01
      #单元化
      logicUnitId: 
      phyUnitId:
---
# Source: payment-parent/charts/payment-cof/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: payment-payment-cof-configmap
  namespace: onebank
  labels:
    app.kubernetes.io/name: payment-cof
    app.kubernetes.io/instance: payment
    app.kubernetes.io/component: payment-cof
    app.kubernetes.io/managed-by: Helm
data:
  bootstrap-fat.yml: |
    server:
      #web端口
      port: 12083
    spring:
      cloud:
        nacos:
          discovery:
            server-addr: payment-payment-cof.onebank.svc.cluster.local:8848
            prefer-ip-address: false  # 禁用IP注册
            use-hostname: true        # 使用K8s Service名称
      datasource:
        username: ENS_FAT
        password: ENC(7f003151f1d531102ab890b5513fb180)
        driver-class-name: oracle.jdbc.OracleDriver
        url: *******************************************
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
          #初始化物理连接个数glCore
          initial-size: 20
          #最小连接池数量
          min-idle: 20
          #最大连接池数量
          max-active: 200
          # 配置获取连接等待超时的时间
          max-wait: 600000
          # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          time-between-eviction-runs-millis: 60000
          # 配置一个连接在池中最小生存的时间，单位是毫秒
          min-evictable-idle-time-millis: 300000
          #用来检测连接是否有效的sql，要求是一个查询语句,如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会其作用
          validation-query: SELECT 'X' FROM DUAL
          #申请连接的时候检测，如果空闲时间大于imeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
          test-while-idle: true
          #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
          test-on-borrow: false
          #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
          test-on-return: false
          # 通过connectProperties属性来打开mergeSql功能；慢SQL记录

          # 合并多个DruidDataSource的监控数据
          use-global-data-source-stat: false
          # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
          filters: slf4j
          # 打开PSCache，并且指定每个连接上PSCache的大小
          pool-prepared-statements: true
          #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
          max-pool-prepared-statement-per-connection-size: 20
          #最小空闲连接数保活
          keep-alive: true
          #以下为基于spring boot web的内嵌druid监控，若需开启请将三个值均置为true
          stat-view-servlet:
            enabled: false
          web-stat-filter:
            enabled: false
          filter:
            stat: false
          fail-fast: true
    platform:
      eureka: ***************:9527
      nacos:
        enable: true
        address: **************:8848
      kafka: dcits.cbs.kafka.sit1:9092
      mars: **************:9994
      gateway: dcits.cbs.gateway.sit1:8081
      redis:
        host: **************
        port: 6379
      apollo:
        enabled: false
        cluster: dev
        url: http://dcits.cbs.apoconf.sit1:8080

    sso:
      url: http://**************:8085

    Url: http://**************:12081/adapter/galaxy
    bicenterUrl: http://**************:13097/bicenter/RegularReport
    glCoreUrl: http://127.0.0.1:9009
    # bp管理端交易
    bpUrl: http://127.0.0.1:9003/bp

    mybatis:
      #mybatis拓展属性文件路径
      config-location: classpath:mybatis/mybatis-config-oracle.xml
      #mysql数据库驱动下，指定tdsql数据库，确保运行sql使用的是tdsql专用的方言sql
      #configurationProperties:
      #  databaseId: tdsql

    sftp:
      client:
        protocol: shareFile
        # ip地址
        host: **************
        # 端口
        port: 22
        # 用户名
        username: fat
        # 密码
        password: fat202101
        # 根路径
        root: /home/<USER>/share/
        # 本地根路径
        lroot: /home/<USER>/share/
        # 密钥文件路径
        privateKey:
        # 密钥的密码
        passphrase: 
        #se
        sessionStrictHostKeyChecking: false
        # session连接超时时间
        sessionConnectTimeout: 15000
        # channel连接超时时间
        channelConnectedTimeout: 15000
        # 大文件拆分--拆分文件存放目录
        fileSplitDir: split
        # 大文件拆分--文件大小，单位MB，超过该配置大小在和sftp.client.fileSplitPercent配置项合并计算，决定是否拆分文件
        fileSplitSize: 2
        # 大文件拆分--超出范围百分比，单位%，1-100的数，超出设定的文件大小后，超出部分超出对应百分比后才进行拆分
        fileSplitPercent: 20
        # 大文件拆分--拆分后文件记录行数
        fileSplitLines: 20000

    ## nacos配置
    galaxy:
      tenantId: online-batch
      profile: fat
      #  4.0配置
      #  availableZone: dc01
      #  gateway: http://comet-gateway-service
      #  tenant: nebula
      #  version: 1.1.0
      #  workspace: system
      appId: SmartGl
      appIdTwo: SMARTGL-REPORT
      # 所属数据中心,多中心部署时配置
      dataCenter: dc01
      #单元化
      logicUnitId: 
      phyUnitId:
---
# Source: payment-parent/charts/payment-gmh/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: payment-payment-gmh-service
  namespace: onebank
  labels:
    helm.sh/chart: payment-gmh-0.1.0
    app.kubernetes.io/name: payment-gmh
    app.kubernetes.io/instance: payment
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: payment-gmh
    app.kubernetes.io/instance: payment
---
# Source: payment-parent/charts/payment-cof/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: payment-payment-cof-service
  namespace: onebank
  labels:
    helm.sh/chart: payment-cof-0.1.0
    app.kubernetes.io/name: payment-cof
    app.kubernetes.io/instance: payment
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: payment-cof
    app.kubernetes.io/instance: payment
---
# Source: payment-parent/charts/payment-gmh/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-payment-gmh
  namespace: onebank
  labels:
    helm.sh/chart: payment-gmh-0.1.0
    app.kubernetes.io/name: payment-gmh
    app.kubernetes.io/instance: payment
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas:
  selector:
    matchLabels:
      app.kubernetes.io/name: payment-gmh
      app.kubernetes.io/instance: payment
  template:
    metadata:
      labels:
        helm.sh/chart: payment-gmh-0.1.0
        app.kubernetes.io/name: payment-gmh
        app.kubernetes.io/instance: payment
        app.kubernetes.io/version: "1.16.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext: null
      # init container for log directory initialization,
      # other directories which need to be granted to app user also need to be initialized(like NFS)
      initContainers:
        - command:
            - sh
            - -c
            - chown 1000:1000 /logs/payment-gmh
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /logs/payment-gmh
              name: log-volume
      containers:
        - name: payment-gmh
          securityContext: null
          image: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/payment-gmh:0.0.5-alpha"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 50m
              memory: 64Mi
          volumeMounts:
            - name: log-volume
              mountPath: /logs/payment-gmh
            # other volume mounts
            - name: configuration-volume
              mountPath: /data/app/config/application-fat.yml
              subPath: application-fat.yml
      volumes:
        - name: log-volume
          hostPath:
            path: /home/<USER>/logs/payment-gmh
            type: DirectoryOrCreate
        # other volume mounts
        - configMap:
            name: payment-payment-gmh-configmap
          name: configuration-volume
---
# Source: payment-parent/charts/payment-cof/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-payment-cof
  namespace: onebank
  labels:
    helm.sh/chart: payment-cof-0.1.0
    app.kubernetes.io/name: payment-cof
    app.kubernetes.io/instance: payment
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas:
  selector:
    matchLabels:
      app.kubernetes.io/name: payment-cof
      app.kubernetes.io/instance: payment
  template:
    metadata:
      labels:
        helm.sh/chart: payment-cof-0.1.0
        app.kubernetes.io/name: payment-cof
        app.kubernetes.io/instance: payment
        app.kubernetes.io/version: "1.16.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext: null
      initContainers:
        - command:
            - sh
            - -c
            - chown 1000:1000 /logs/payment-cof
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
          imagePullPolicy: IfNotPresent
          name: init-dir
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /logs/payment-cof
              name: log-volume
      containers:
        - name: payment-cof
          securityContext: null
          image: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/payment-cof:0.0.5-alpha"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 50m
              memory: 64Mi
          volumeMounts:
            - name: log-volume
              mountPath: /logs/payment-cof
            - name: configuration-volume
              mountPath: /data/app/config/bootstrap-fat.yml
              subPath: bootstrap-fat.yml
      volumes:
        - name: log-volume
          hostPath:
            path: /home/<USER>/logs/payment-cof
            type: DirectoryOrCreate
        - configMap:
            name: payment-payment-cof-configmap
          name: configuration-volume
