# Default values for microservices chart
# This is a YAML-formatted file.

# Global configuration
global:
  # Default namespace for all services
  namespace: "onebank"

  # Default image host
  imageHost: "************.dkr.ecr.ap-southeast-1.amazonaws.com"

  # Default image pull policy
  imagePullPolicy: "IfNotPresent"

  # Default timezone
  timezone: "Asia/Shanghai"

  # Default init container image for log directory initialization
  initContainer:
    image: "busybox:1.35"
    imagePullPolicy: "IfNotPresent"

  # Node affinity configuration for pod distribution
  nodeAffinity:
    # Enable pod anti-affinity to spread pods across nodes
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Pod anti-affinity configuration
  podAntiAffinity:
    # Enable pod anti-affinity to avoid multiple pods on same node
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Default resource limits and requests
  defaultResources:
    limits:
      cpu: "1000m"
      memory: "1Gi"
    requests:
      cpu: "100m"
      memory: "128Mi"

  # Default security context
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000

  # Default middleware configuration
  middleware:
    # Default database configuration
    defaultDatabase:
      databaseType: MySQL #数据库类型：Oracle，MySQL ...
      host: "********************************************************************************************************************"
      username: "root"
      password: "rootpasswd#45r"

    # Default platform configuration
    defaultPlatform:
      eureka: ***************:9527
      sequences:
        enable: true
        address: **************:9004
        loadBalance: roundRobin #路由策略
        failPolicy: failover #序列申请失败处理策略failover/failfast 默认failover
        retry: #在failover模式下的尝试次数
          times: 3 #在failover模式下的尝试次数
          interval: 100 #重试间隔
        message:
          timeout: 1000 #通讯超时时间，单次获取序列超时时间
      nacos:
        enable: true
        address: nacos-standalone-nacos.paas.cluster.svc.local:8848
        tenant: T000001
        workspace: W000001
        clusterName: onebank
        preferIpAddress: false
        useHostname: true
      orbit:
        enable: true
      mars: **************:9994
      gateway: dcits.cbs.gateway.sit1:8081
      sonic:
        executor:
          #是否启用执行端服务,默认开启，如需关闭可设为false
          enabled: false
          clientProfile:
            machineId: 001
            schedulerAddresses: **************:9999
            beanGeneratorType: springBean
            #执行端模式
            #0 生产模式 默认值
            #1 测试模式 测试模式下step执行异常将会自动转换为待确认异常，通过管理端可直接确认、跳过
            #mode: 0
          lock:
            #是否开启执行锁，默认false
            enable: true
            #数据库类型 mysql,oracle,db2,sqlserver,oceanbase
            dbType: mysql
          stepCache:
            enable: true
            cacheType: db
      mq:
        producer: **************:13070
        consumer: **************:13070
      redis:
        host: redis-standalone-redis.paas.svc.cluster.local
        port: 6379
        password:
        database:
      file:
        passwd: cbs001
        serverIp: ***************
        port: 5001
      apollo:
        enable: false
        cluster: dev
        url: http://dcits.cbs.apoconf.sit1:8080
      skywalking:
        path: /app/dcits/app-run/agent
        url: dcits.cbs.skywalking.sit1:11800
      sso:
        url: http://**************:8085

    # Default sftp configuration
    defaultSftp:
      client:
        protocol: shareFile
        host: **************
        port: 22
        username: fat
        password: fat202101
        root: /home/<USER>/share/
        lroot: /home/<USER>/share/
        privateKey:
        passphrase:
        sessionStrictHostKeyChecking: no
        sessionConnectTimeout: 15000
        channelConnectedTimeout: 15000
        fileSplitDir: split
        fileSplitSize: 2
        fileSplitPercent: 20
        fileSplitLines: 20000

    # Default jupiter configuration
    defaultJupiter:
      mesh:
        enable: false
      metrics:
        enable: true
      unitized:
        enable: false
        app:

    # Default galaxy configuration
    defaultGalaxy:
      tenantId: online-batch
      profile: fat
      appId: SmartGl
      appIdTwo: SMARTGL-REPORT
      dataCenter: dc01
      logicUnitId:
      phyUnitId:

# Micro-Services
payment-gmh:
  enabled: true
payment-cof:
  enabled: true
