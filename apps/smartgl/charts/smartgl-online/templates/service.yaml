apiVersion: v1
kind: Service
metadata:
  name: {{ include "smartgl-online.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "smartgl-online.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "smartgl-online.selectorLabels" . | nindent 4 }}
