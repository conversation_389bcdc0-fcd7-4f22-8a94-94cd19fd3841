# Default values for core microservices chart
# This is a YAML-formatted file.

# Global configuration
global:
  # Default namespace for all services
  namespace: "onebank"
  
  # Default image registry
  imageRegistry: ""
  
  # Default image pull policy
  imagePullPolicy: "IfNotPresent"
  
  # Default timezone
  timezone: "Asia/Shanghai"
  
  # Default init container image for log directory initialization
  initContainer:
    image: "busybox:1.35"
    imagePullPolicy: "IfNotPresent"

  # Node affinity configuration for pod distribution
  nodeAffinity:
    # Enable pod anti-affinity to spread pods across nodes
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Pod anti-affinity configuration
  podAntiAffinity:
    # Enable pod anti-affinity to avoid multiple pods on same node
    enabled: true
    # Preferred or Required
    type: "preferredDuringSchedulingIgnoredDuringExecution"
    # Weight for preferred affinity
    weight: 100

  # Default resource limits and requests
  defaultResources:
    limits:
      cpu: "1000m"
      memory: "1Gi"
    requests:
      cpu: "100m"
      memory: "128Mi"

  # Default security context
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000

  # Default middleware configuration
  middleware:
    # Default database configuration
    defaultDatabase:
      host: "****************************************************************************"
      username: "root"
      password: "1q2w3e4R"

    # Default platform configuration
    defaultPlatform:
      eureka: dcits.cbs.eureka.fat1:9527
      nacos: 
        enable: true
        address: nacosx-headless.paas:8848
      mars: dcits.cbs.mars.fat1:9994
      gateway: dcits.cbs.gateway.fat1:8081
      sonic: 
        schedulerAddresses: dcits.cbs.sonic.fat1:9999
        serviceAddresses: dcits.cbs.sonic.fat1:8089
        lock:
          dbType: mysql  
      mq: 
        producer: rocketmq-nameserver.paas:9876
        consumer: rocketmq-nameserver.paas:9876
      redis: 
        host: redis-standalone-redis.paas
        #host: dcits.cbs.redis.fat1
        port: 6379
        password: ""
      apollo: 
        enable: false
        cluster: fat
        url: http://dcits.cbs.apoconf.fat1:8080
      skywalking:
        path: /app/dcits/app-run/agent
        url: dcits.cbs.skywalking.fat1:11800
    # Default sftp configuration
    defaultSftp:
      client:
        protocol: shareFile
        host: ***************
        port: 22
        username: dcits
        password: dcits@193!
        root: /app/dcits/share
        sessionStrictHostKeyChecking: no
        sessionConnectTimeout: 15000
        channelConnectedTimeout: 15000

    # Default galaxy configuration
    defaultGalaxy:
      tenantId: fcsjlwj1gk9s
      profile: y6vklwj509pg
      dataCenter: dc01

# Micro-Services
ensemble-cif-service:
  enabled: false
ensemble-cl-service:
  enabled: false
ensemble-cloud-tae-service:
  enabled: false
ensemble-ob-service:
  enabled: true
ensemble-pf-service:
  enabled: false
ensemble-rb-service:
  enabled: false
ensemble-tae-service:
  enabled: false 