# core Helm Charts 说明

## 目录结构

```
core/
├── Chart.yaml                    # 主chart定义，包含7个子chart依赖
├── values.yaml                   # 主chart全局配置
└── charts/                       # 子chart目录
    ├── ensemble-cif-service/     # CIF服务子chart
    │   ├── Chart.yaml
    │   ├── values.yaml          # 包含application-fat.yml配置
    │   └── templates/
    │       ├── _helpers.tpl
    │       ├── configmap.yaml
    │       ├── deployment.yaml
    │       └── service.yaml
    ├── ensemble-cl-service/      # CL服务子chart
    ├── ensemble-cloud-tae-service/ # Cloud TAE服务子chart
    ├── ensemble-ob-service/      # OB服务子chart
    ├── ensemble-pf-service/      # PF服务子chart
    ├── ensemble-rb-service/      # RB服务子chart
    └── ensemble-tae-service/     # TAE服务子chart
```

## 主要特性

### 1. 主Chart (core-parent)
- `Chart.yaml`：定义了7个服务的依赖关系
- `values.yaml`：包含全局配置（namespace、middleware、sftp等）

### 2. 子Chart (每个服务)
- `Chart.yaml`：服务元数据
- `values.yaml`：服务特定配置，包含完整的 `application-fat.yml` 内容
- `templates/`：标准Kubernetes资源模板
  - `_helpers.tpl`：通用模板函数
  - `configmap.yaml`：配置文件注入
  - `deployment.yaml`：容器部署
  - `service.yaml`：服务暴露

### 3. 配置管理
- 每个服务的 `application-fat.yml` 配置已完整嵌入到对应的 `values.yaml` 中
- 支持通过 ConfigMap 挂载到容器
- 配置目录默认为 `/data/app/config`

### 4. 部署特性
- 支持副本数配置
- 支持资源限制和请求
- 支持健康检查（liveness/readiness probe）
- 支持日志目录挂载
- 支持持久化存储（可选）

## 使用方法

### 1. 安装整个模块
```bash
helm install core ./core
```

### 2. 只安装特定服务
```bash
helm install core ./core --set ensemble-cif-service.enabled=true --set ensemble-cl-service.enabled=false
```

### 3. 自定义配置
```bash
helm install core ./core -f custom-values.yaml
```

---

所有服务都已按照 smartgl 模块的格式规范创建完成，可以直接使用！ 