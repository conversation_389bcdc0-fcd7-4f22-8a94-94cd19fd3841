platform:
  eureka: {{ .Values.global.middleware.defaultPlatform.eureka | quote }}
  nacos: {{ .Values.global.middleware.defaultPlatform.nacos.address | default "" | quote }}
  sonic:
    schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.schedulerAddresses | default "" | quote }}
    serviceAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.serviceAddresses | default "" | quote }}
    lock:
      db-type: {{ .Values.global.middleware.defaultPlatform.sonic.lock.dbType | default "mysql" | quote }}
  mars: {{ .Values.global.middleware.defaultPlatform.mars | default "" | quote }}
  gateway: {{ .Values.global.middleware.defaultPlatform.gateway | default "" | quote }}
  redis:
    host: {{ .Values.global.middleware.defaultPlatform.redis.host | default "" | quote }}
    port: {{ .Values.global.middleware.defaultPlatform.redis.port | default "6379" | quote }}
  mq:
    producer: {{ .Values.global.middleware.defaultPlatform.mq.producer | default "" | quote }}
    consumer: {{ .Values.global.middleware.defaultPlatform.mq.consumer | default "" | quote }}
  apollo:
    enabled: {{ .Values.global.middleware.defaultPlatform.apollo.enable | default "false" | quote }}
    cluster: {{ .Values.global.middleware.defaultPlatform.apollo.cluster | default "" | quote }}
    url: {{ .Values.global.middleware.defaultPlatform.apollo.url | default "" | quote }}

libra:
  configUrl: "libra/mysql"
  decrypt: "0"
  upright:
    url: {{ printf "%s/%s?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.clDbHost .Values.clDbName | quote }}
    user: {{ .Values.clDbUser | quote }}
    password: {{ .Values.clDbPassword | quote }}
  level1:
    url: {{ printf "%s/%s001?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.clDbHost .Values.clDbName | quote }}
    user: {{ .Values.clDbUser | quote }}
    password: {{ .Values.clDbPassword | quote }}
  level2:
    url: {{ printf "%s/%s002?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.clDbHost .Values.clDbName | quote }}
    user: {{ .Values.clDbUser | quote }}
    password: {{ .Values.clDbPassword | quote }}
  level3:
    url: {{ printf "%s/%s003?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.clDbHost .Values.clDbName | quote }}
    user: {{ .Values.clDbUser | quote }}
    password: {{ .Values.clDbPassword | quote }}
  level4:
    url: {{ printf "%s/%s004?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.clDbHost .Values.clDbName | quote }}
    user: {{ .Values.clDbUser | quote }}
    password: {{ .Values.clDbPassword | quote }}

mybatis:
  config-location: "classpath:mybatis/mybatis-config-mysql.xml"

galaxy:
  tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId | default "" | quote }}
  profile: {{ .Values.global.middleware.defaultGalaxy.profile | default "" | quote }}
  dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter | default "" | quote }}

jasypt:
  encryptor:
    bean: "sm4CBCJasypt"

com:
  dcits:
    path:
      localFilePath: "/home/<USER>/switch/file/CL/"
      targetFilePath: "/home/<USER>/share/"
      clTargetFilePath: "/home/<USER>/share/file/cl_rec/"
      shareFilePath: "/home/<USER>/share/"
      simulationRetailPath: "/home/<USER>/switch/file/simulation/RB/"
      icmsFilePath: "/home/<USER>/transfer/upload/"

sofr:
  flag: true 