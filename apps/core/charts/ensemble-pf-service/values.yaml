ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

replicas: 1
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

image:
  repository: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/ensemble-pf-service"
  tag: "0.0.1-alpha"
  pullPolicy: "IfNotPresent"

configDirectory: "/data/app/config"
logDirectory: "/logs"

service:
  type: "ClusterIP"
  port: 8080
  targetPort: 8080

resources:
  limits:
    cpu: "500m"
    memory: "512Mi"
  requests:
    cpu: "50m"
    memory: "64Mi"

env:
  - name: "DB_HOST"
    value: "mysql-service"
  - name: "DB_PORT"
    value: "3306"

persistentVolume:
  enabled: false
  storageClass: ""
  accessMode: "ReadWriteOnce"
  size: "1Gi"
  mountPath: "/app/data"

volumeMounts:
  - name: "logs"
    mountPath: "/logs"

volumes:
  - name: "logs"
    hostPath:
      path: /home/<USER>/logs
      type: DirectoryOrCreate

livenessProbe:
  httpGet:
    path: "/health"
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: "/ready"
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5

# 服务专属变量
pfDbName: ens_pf
pfDbUser: ENS_PF
pfDbPassword: 1q2w3e4R!@#$
pfDbHost: ****************************************************************************

configMap:
  enabled: true

configMap:
  enabled: true
  data:
    application-fat.yml: |
      platform:
        eureka: {{ .Values.global.middleware.defaultPlatform.eureka }}
        nacos: {{ .Values.global.middleware.defaultPlatform.nacos.address }}
        sonic:
          schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.schedulerAddresses }}
          lock:
            db-type: mysql
        mars: {{ .Values.global.middleware.defaultPlatform.mars }}
        gateway: {{ .Values.global.middleware.defaultPlatform.gateway }}
        redis:
          host: {{ .Values.global.middleware.defaultPlatform.redis.host }}
          port: {{ .Values.global.middleware.defaultPlatform.redis.port }}
          password: {{ .Values.global.middleware.defaultPlatform.redis.password | quote }}
        apollo:
          enabled: {{ .Values.global.middleware.defaultPlatform.apollo.enable }}
          cluster: {{ .Values.global.middleware.defaultPlatform.apollo.cluster }}
          url: {{ .Values.global.middleware.defaultPlatform.apollo.url }}

      libra:
        configUrl: libra/mysql
        decrypt: 0
        upright:
          url: {{ printf "%s/%s?characterEncoding=utf-8&useSSL=false" .Values.pfDbHost .Values.pfDbName }}
          user: {{ .Values.pfDbUser }}
          password: {{ .Values.pfDbPassword | quote }}

      mybatis:
        config-location: classpath:mybatis/mybatis-config-mysql.xml

      galaxy:
        tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId | default "${TENANT}" }}
        profile: {{ .Values.global.middleware.defaultGalaxy.profile | default "${WORKSPACE}" }}
        dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter | default "${AVAILABLEZONE}" }}

      jasypt:
        encryptor:
          bean: sm4CBCJasypt 