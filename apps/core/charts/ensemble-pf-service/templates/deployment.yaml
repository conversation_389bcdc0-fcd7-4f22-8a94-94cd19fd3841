apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ensemble-pf-service.fullname" . }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "ensemble-pf-service.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicas }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "ensemble-pf-service.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "ensemble-pf-service.labels" . | nindent 8 }}
	{{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.securityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          {{- with .Values.livenessProbe }}
          livenessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.readinessProbe }}
          readinessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- range . }}
            - name: {{ .name }}
              mountPath: {{ tpl (.mountPath | default "/logs") $ }}
            {{- end }}
          {{- end }}
          {{- if .Values.configMap.enabled }}
          volumeMounts:
            - name: configmap-volume
              mountPath: {{ tpl .Values.configDirectory . }}
          {{- end }}
          {{- if .Values.persistentVolume.enabled }}
          volumeMounts:
            - name: pv-volume
              mountPath: {{ .Values.persistentVolume.mountPath | default "/app/data" }}
          {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.configMap.enabled }}
      volumes:
        - name: configmap-volume
          configMap:
            name: {{ include "ensemble-pf-service.fullname" . }}-configmap
      {{- end }}
      {{- if .Values.persistentVolume.enabled }}
      volumes:
        - name: pv-volume
          persistentVolumeClaim:
            claimName: {{ include "ensemble-pf-service.fullname" . }}-pvc
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }} 