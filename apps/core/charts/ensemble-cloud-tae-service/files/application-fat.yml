platform:
  eureka: {{ .Values.global.middleware.defaultPlatform.eureka | quote }}
  nacos: {{ .Values.global.middleware.defaultPlatform.nacos.address | default "**********:8848" | quote }}
  sonic:
    schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.schedulerAddresses | default "**********:9999" | quote }}
    serviceAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.serviceAddresses | default "**********:8089" | quote }}
    lock:
      db-type: {{ .Values.global.middleware.defaultPlatform.sonic.lock.dbType | default "mysql" | quote }}
  mars: {{ .Values.global.middleware.defaultPlatform.mars | default "**********:9994" | quote }}
  gateway: {{ .Values.global.middleware.defaultPlatform.gateway | default "**********:8081" | quote }}
  redis:
    host: {{ .Values.global.middleware.defaultPlatform.redis.host | default "**********" | quote }}
    port: {{ .Values.global.middleware.defaultPlatform.redis.port | default "6379" | quote }}
    password: {{ .Values.global.middleware.defaultPlatform.redis.password | quote }}
  mq:
    producer: {{ .Values.global.middleware.defaultPlatform.mq.producer | default "**********:9876" | quote }}
    consumer: {{ .Values.global.middleware.defaultPlatform.mq.consumer | default "**********:9876" | quote }}
  apollo:
    enabled: {{ .Values.global.middleware.defaultPlatform.apollo.enable | default "false" | quote }}
    cluster: {{ .Values.global.middleware.defaultPlatform.apollo.cluster | default "" | quote }}
    url: {{ .Values.global.middleware.defaultPlatform.apollo.url | default "" | quote }}

libra:
  configUrl: "libra/mysql"
  decrypt: "0"
  upright:
    url: {{ printf "%s/%s?useSSL=false" .Values.cloudTaeDbHost .Values.cloudTaeDbName | quote }}
    user: {{ .Values.cloudTaeDbUser | quote }}
    password: {{ .Values.cloudTaeDbPassword | quote }}
  level1:
    url: {{ printf "%s/%s001?useSSL=false" .Values.cloudTaeDbHost .Values.cloudTaeDbName | quote }}
    user: {{ .Values.cloudTaeDbUser | quote }}
    password: {{ .Values.cloudTaeDbPassword | quote }}
  level2:
    url: {{ printf "%s/%s002?useSSL=false" .Values.cloudTaeDbHost .Values.cloudTaeDbName | quote }}
    user: {{ .Values.cloudTaeDbUser | quote }}
    password: {{ .Values.cloudTaeDbPassword | quote }}
  level3:
    url: {{ printf "%s/%s003?useSSL=false" .Values.cloudTaeDbHost .Values.cloudTaeDbName | quote }}
    user: {{ .Values.cloudTaeDbUser | quote }}
    password: {{ .Values.cloudTaeDbPassword | quote }}
  level4:
    url: {{ printf "%s/%s004?useSSL=false" .Values.cloudTaeDbHost .Values.cloudTaeDbName | quote }}
    user: {{ .Values.cloudTaeDbUser | quote }}
    password: {{ .Values.cloudTaeDbPassword | quote }}

galaxy:
  tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId | default "fcsjlwj1gk9s" | quote }}
  profile: {{ .Values.global.middleware.defaultGalaxy.profile | default "y6vklwj509pg" | quote }}
  dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter | default "dc01" | quote }}

mybatis:
  config-location: "classpath:mybatis/mybatis-config.xml"
  configuration:
    database-id: "mysql"

jasypt:
  encryptor:
    bean: "sm4CBCJasypt" 