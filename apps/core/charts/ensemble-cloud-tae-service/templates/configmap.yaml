{{- if .Values.configMap.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "ensemble-cloud-tae-service.fullname" . }}-configmap
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    app.kubernetes.io/name: {{ include "ensemble-cloud-tae-service.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: ensemble-cloud-tae-service
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  application-fat.yml: |-
    {{- tpl (.Files.Get "files/application-fat.yml") . | nindent 4 }}
{{- end }} 