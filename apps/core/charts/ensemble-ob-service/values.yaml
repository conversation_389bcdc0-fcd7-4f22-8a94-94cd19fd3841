ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

replicas: 1
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

image:
  repository: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/ensemble-ob-service"
  tag: "0.0.1-alpha"
  pullPolicy: "IfNotPresent"

podSecurityContext:
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000

containerSecurityContext:
  runAsUser: 1000
  runAsGroup: 1000

configDirectory: "/home/<USER>/app-run/conf"
logDirectory: "/home/<USER>/logs"

service:
  type: "ClusterIP"
  port: 8080
  targetPort: 8080

resources:
  limits:
    cpu: "1000m"
    memory: "2500Mi"
  requests:
    cpu: "50m"
    memory: "1800Mi"

env:
  - name: "DB_HOST"
    value: "mysql-service"
  - name: "DB_PORT"
    value: "3306"

persistentVolume:
  enabled: false
  storageClass: ""
  accessMode: "ReadWriteOnce"
  size: "1Gi"
  mountPath: "/app/data"

volumeMounts:
  - name: "logs"
    mountPath: "/home/<USER>/logs"

volumes:
  - name: "logs"
    hostPath:
      path: /home/<USER>/logs
      type: DirectoryOrCreate

livenessProbe:
  httpGet:
    path: "/health"
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: "/ready"
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5

# 服务专属变量
obDbName: ens_ob
obDbUser: root
obDbPassword: rootpasswd#45r
obDbHost: *******************************************************************************

configMap:
  enabled: true