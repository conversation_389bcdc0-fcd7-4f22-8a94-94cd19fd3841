platform:
  eureka: {{ .Values.global.middleware.defaultPlatform.eureka }}
  nacos: {{ .Values.global.middleware.defaultPlatform.nacos.address }}
  sonic:
    schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.schedulerAddresses }}
    lock:
      db-type: {{ .Values.global.middleware.defaultPlatform.sonic.lock.dbType | default "mysql" }}
  mars: {{ .Values.global.middleware.defaultPlatform.mars }}
  gateway: {{ .Values.global.middleware.defaultPlatform.gateway }}
  kafka: {{ .Values.global.middleware.defaultPlatform.kafka }}
  redis:
    host: {{ .Values.global.middleware.defaultPlatform.redis.host }}
    port: {{ .Values.global.middleware.defaultPlatform.redis.port }}
    password: {{ .Values.global.middleware.defaultPlatform.redis.password | quote }}
  mq:
    producer: {{ .Values.global.middleware.defaultPlatform.mq.producer }}
    consumer: {{ .Values.global.middleware.defaultPlatform.mq.consumer }}
  apollo:
    enabled: {{ .Values.global.middleware.defaultPlatform.apollo.enable | default "false" }}
    cluster: {{ .Values.global.middleware.defaultPlatform.apollo.cluster }}
    url: {{ .Values.global.middleware.defaultPlatform.apollo.url }}

libra:
  configUrl: "libra/mysql"
  decrypt: "0"
  upright:
    url: {{ printf "%s/%s?characterEncoding=utf-8&useSSL=false" .Values.obDbHost .Values.obDbName }}
    user: {{ .Values.obDbUser }}
    password: {{ .Values.obDbPassword | quote }}

mybatis:
  config-location: "classpath:mybatis/mybatis-config-mysql.xml"

logging:
  #logback配置文件加载路径
  config: classpath:logback-app.xml
  #log文件父级路径
  file:
    path: /home/<USER>/logs
    #单个日志文件最多 10MB, 15天的日志周期，最大不能超过10GB
    max-size: 10MB
    max-history: 15
    total-size-cap: 10GB

galaxy:
  tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId }}
  profile: {{ .Values.global.middleware.defaultGalaxy.profile }}
  dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter }}

jasypt:
  encryptor:
    bean: "sm4CBCJasypt"

com:
  dcits:
    path:
      localFilePath: "/home/<USER>/switch/file/OB/"
      targetFilePath: "/home/<USER>/share/"

mobile:
  bank:
    service: "**********:9084"
    enable: true

company:
  intbank:
    service: "**********:9080"
    enable: false 