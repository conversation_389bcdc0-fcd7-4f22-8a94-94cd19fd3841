apiVersion: v1
kind: Service
metadata:
  name: {{ include "ensemble-ob-service.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "ensemble-ob-service.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "ensemble-ob-service.selectorLabels" . | nindent 4 }} 