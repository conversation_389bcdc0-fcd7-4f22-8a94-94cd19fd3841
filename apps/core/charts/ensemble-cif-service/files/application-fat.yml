platform:
  eureka: {{ .Values.global.middleware.defaultPlatform.eureka | quote }}
  nacos: {{ .Values.global.middleware.defaultPlatform.nacos.address | default "" | quote }}
  sonic:
    schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.schedulerAddresses | default "" | quote }}
    lock:
      db-type: {{ .Values.global.middleware.defaultPlatform.sonic.lock.dbType | default "mysql" | quote }}
  mars: {{ .Values.global.middleware.defaultPlatform.mars | default "" | quote }}
  gateway: {{ .Values.global.middleware.defaultPlatform.gateway | default "" | quote }}
  redis:
    host: {{ .Values.global.middleware.defaultPlatform.redis.host | default "" | quote }}
    port: {{ .Values.global.middleware.defaultPlatform.redis.port | default "3306" | quote }}
    password: {{ .Values.global.middleware.defaultPlatform.redis.password | quote }}
  mq:
    producer: {{ .Values.global.middleware.defaultPlatform.mq.producer | default "" | quote }}
    consumer: {{ .Values.global.middleware.defaultPlatform.mq.consumer | default "" | quote }}
  apollo:
    enabled: {{ .Values.global.middleware.defaultPlatform.apollo.enable | default "false" | quote }}
    cluster: {{ .Values.global.middleware.defaultPlatform.apollo.cluster | default "" | quote }}
    url: {{ .Values.global.middleware.defaultPlatform.apollo.url | default "" | quote }}

libra:
  configUrl: "libra/mysql"
  upright:
    url: {{ printf "%s/%s?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName | quote }}
    user: {{ .Values.cifDbUser | quote }}
    password: {{ .Values.cifDbPassword | quote }}
  level1:
    url: {{ printf "%s/%s001?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName | quote }}
    user: {{ .Values.cifDbUser | quote }}
    password: {{ .Values.cifDbPassword | quote }}
  level2:
    url: {{ printf "%s/%s002?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName | quote }}
    user: {{ .Values.cifDbUser | quote }}
    password: {{ .Values.cifDbPassword | quote }}
  level3:
    url: {{ printf "%s/%s003?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName | quote }}
    user: {{ .Values.cifDbUser | quote }}
    password: {{ .Values.cifDbPassword | quote }}
  level4:
    url: {{ printf "%s/%s004?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName | quote }}
    user: {{ .Values.cifDbUser | quote }}
    password: {{ .Values.cifDbPassword | quote }}

mybatis:
  config-location: "classpath:mybatis/mybatis-config-mysql.xml"

com:
  dcits:
    path:
      localFilePath: "/home/<USER>/switch/file/CIF/"
      targetFilePath: "/home/<USER>/share/"
    encrypt:
      ip: "***********"
      port: "11112"
      keyName: "CBS.CBS.PVK"
      checkData: "CBS"
      keyName2: "hx.rmm.zpk"
      cvvKeyName: "CBS.CBS.CVK"

sftp:
  client:
    protocol: {{ .Values.global.middleware.defaultSftp.client.protocol | default "" | quote }}
    root: {{ .Values.global.middleware.defaultSftp.client.root | default "" | quote }}
    username: {{ .Values.global.middleware.defaultSftp.client.username | default "" | quote }}
    password: {{ .Values.global.middleware.defaultSftp.client.password | quote }}
    port: {{ .Values.global.middleware.defaultSftp.client.port | default "22" | quote }}
    host: {{ .Values.global.middleware.defaultSftp.client.host | default "" | quote }}
    sessionStrictHostKeyChecking: {{ .Values.global.middleware.defaultSftp.client.sessionStrictHostKeyChecking | default "no" | quote }}
    sessionConnectTimeout: {{ .Values.global.middleware.defaultSftp.client.sessionConnectTimeout | default "" | quote }}
    channelConnectedTimeout: {{ .Values.global.middleware.defaultSftp.client.channelConnectedTimeout | default "" | quote }}

galaxy:
  tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId | default "" | quote }}
  profile: {{ .Values.global.middleware.defaultGalaxy.profile | default "" | quote }}
  dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter | default "" | quote }}

jasypt:
  encryptor:
    bean: "sm4CBCJasypt" 