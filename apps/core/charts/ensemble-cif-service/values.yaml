ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

replicas: 1
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

image:
  repository: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/ensemble-cif-service"
  tag: "0.0.1-alpha"
  pullPolicy: "IfNotPresent"

configDirectory: "/data/app/config"
logDirectory: "/logs"

service:
  type: "ClusterIP"
  port: 8080
  targetPort: 8080

resources:
  limits:
    cpu: "500m"
    memory: "512Mi"
  requests:
    cpu: "50m"
    memory: "64Mi"

env:
  - name: "DB_HOST"
    value: "mysql-service"
  - name: "DB_PORT"
    value: "3306"

persistentVolume:
  enabled: false
  storageClass: ""
  accessMode: "ReadWriteOnce"
  size: "1Gi"
  mountPath: "/app/data"

volumeMounts:
  - name: "logs"
    mountPath: "/logs"

volumes:
  - name: "logs"
    hostPath:
      path: /home/<USER>/logs
      type: DirectoryOrCreate

livenessProbe:
  httpGet:
    path: "/health"
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: "/ready"
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5

# 服务专属变量
cifDbName: ENS_CIF
cifDbUser: ENS_CIF
cifDbPassword: 1q2w3e4R!@#$
cifDbHost: ****************************************************************************

configMap:
  enabled: true

configMap:
  enabled: true
  data:
    application-fat.yml: |
      platform:
        eureka: {{ .Values.global.middleware.defaultPlatform.eureka }}
        nacos: {{ .Values.global.middleware.defaultPlatform.nacos.address }}
        sonic:
          schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.schedulerAddresses }}
          lock:
            db-type: mysql
        mars: {{ .Values.global.middleware.defaultPlatform.mars }}
        gateway: {{ .Values.global.middleware.defaultPlatform.gateway }}
        redis:
          host: {{ .Values.global.middleware.defaultPlatform.redis.host }}
          port: {{ .Values.global.middleware.defaultPlatform.redis.port }}
          password: {{ .Values.global.middleware.defaultPlatform.redis.password | quote }}
        mq:
          producer: {{ .Values.global.middleware.defaultPlatform.mq.producer }}
          consumer: {{ .Values.global.middleware.defaultPlatform.mq.consumer }}
        apollo:
          enabled: {{ .Values.global.middleware.defaultPlatform.apollo.enable }}
          cluster: {{ .Values.global.middleware.defaultPlatform.apollo.cluster }}
          url: {{ .Values.global.middleware.defaultPlatform.apollo.url }}

      libra:
        configUrl: libra/mysql
        upright:
          url: {{ printf "%s/%s?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName }}
          user: {{ .Values.cifDbUser }}
          password: {{ .Values.cifDbPassword | quote }}
        level1:
          url: {{ printf "%s/%s001?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName }}
          user: {{ .Values.cifDbUser }}
          password: {{ .Values.cifDbPassword | quote }}
        level2:
          url: {{ printf "%s/%s002?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName }}
          user: {{ .Values.cifDbUser }}
          password: {{ .Values.cifDbPassword | quote }}
        level3:
          url: {{ printf "%s/%s003?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName }}
          user: {{ .Values.cifDbUser }}
          password: {{ .Values.cifDbPassword | quote }}
        level4:
          url: {{ printf "%s/%s004?characterEncoding=utf-8&useSSL=false" .Values.cifDbHost .Values.cifDbName }}
          user: {{ .Values.cifDbUser }}
          password: {{ .Values.cifDbPassword | quote }}

      mybatis:
        config-location: classpath:mybatis/mybatis-config-mysql.xml

      com:
        dcits:
          path:
            localFilePath: /home/<USER>/switch/file/CIF/
            targetFilePath: /home/<USER>/share/
          encrypt:
            ip: ***********
            port: 11112
            keyName: CBS.CBS.PVK
            checkData: CBS
            keyName2: hx.rmm.zpk
            cvvKeyName: CBS.CBS.CVK

      sftp:
        client:
          protocol: {{ .Values.global.middleware.defaultSftp.client.protocol }}
          root: {{ .Values.global.middleware.defaultSftp.client.root }}
          username: {{ .Values.global.middleware.defaultSftp.client.username }}
          password: {{ .Values.global.middleware.defaultSftp.client.password | quote }}
          port: {{ .Values.global.middleware.defaultSftp.client.port }}
          host: {{ .Values.global.middleware.defaultSftp.client.host }}
          sessionStrictHostKeyChecking: {{ .Values.global.middleware.defaultSftp.client.sessionStrictHostKeyChecking }}
          sessionConnectTimeout: {{ .Values.global.middleware.defaultSftp.client.sessionConnectTimeout }}
          channelConnectedTimeout: {{ .Values.global.middleware.defaultSftp.client.channelConnectedTimeout }}

      galaxy:
        tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId }}
        profile: {{ .Values.global.middleware.defaultGalaxy.profile }}
        dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter }}

      jasypt:
        encryptor:
          bean: sm4CBCJasypt 