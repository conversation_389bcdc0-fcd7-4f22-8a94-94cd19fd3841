platform:
  eureka: {{ .Values.global.middleware.defaultPlatform.eureka | quote }}
  nacos: {{ .Values.global.middleware.defaultPlatform.nacos.address | default "" | quote }}
  kafka: {{ .Values.global.middleware.defaultPlatform.kafka | default "***************:9092" | quote }}
  sonic:
    schedulerAddresses: {{ .Values.global.middleware.defaultPlatform.sonic.schedulerAddresses | default "" | quote }}
    lock:
      db-type: {{ .Values.global.middleware.defaultPlatform.sonic.lock.dbType | default "mysql" | quote }}
  mars: {{ .Values.global.middleware.defaultPlatform.mars | default "" | quote }}
  redis:
    host: {{ .Values.global.middleware.defaultPlatform.redis.host | default "" | quote }}
    port: {{ .Values.global.middleware.defaultPlatform.redis.port | default "" | quote }}
    password: {{ .Values.global.middleware.defaultPlatform.redis.password | quote }}
  mq:
    producer: {{ .Values.global.middleware.defaultPlatform.mq.producer | default "" | quote }}
    consumer: {{ .Values.global.middleware.defaultPlatform.mq.consumer | default "" | quote }}
  file:
    passwd: {{ .Values.file.passwd | quote }}
    serverIp: {{ .Values.file.serverIp | quote }}
    port: {{ .Values.file.port | quote }}
  apollo:
    enabled: {{ .Values.global.middleware.defaultPlatform.apollo.enable | default "false" | quote }}
    cluster: {{ .Values.global.middleware.defaultPlatform.apollo.cluster | default "" | quote }}
    url: {{ .Values.global.middleware.defaultPlatform.apollo.url | default "" | quote }}
  gateway: {{ .Values.global.middleware.defaultPlatform.gateway | default "" | quote }}

skywalking-path: {{ .Values.global.middleware.defaultPlatform.skywalking.path | default "" | quote }}
skywalking-url: {{ .Values.global.middleware.defaultPlatform.skywalking.url | default "" | quote }}

galaxy:
  tenantId: {{ .Values.global.middleware.defaultGalaxy.tenantId | default "" | quote }}
  profile: {{ .Values.global.middleware.defaultGalaxy.profile | default "" | quote }}
  dataCenter: {{ .Values.global.middleware.defaultGalaxy.dataCenter | default "" | quote }}

com:
  dcits:
    path:
      localFilePath: "/home/<USER>/rb_file/"
      targetFilePath: "/home/<USER>/share/"
      clTargetFilePath: "cl/"
      shareFilePath: "/home/<USER>/share/"
      uploadFilePath: "/apps/data/transfer/upload/ncbs/rb/"
      dcTemplateFilePath: "/apps/svr/ensemble-rb-service/template/"
    job: "defaultOnlineBatchJob"
    target:
      filepath:
        uploadteller: "/home/<USER>/ensemble-cloud/SmartTeller9/teller9_tmp/"
        makecard: "/home/<USER>/config/filepath/upload/"
        makeocfilepath: "/home/<USER>/config/filepath/oc/"
    encrypt:
      ip: "***********"
      port: "11112"
      keyName: "CBS.CBS.PVK"
      checkData: "CBS"
      keyName2: "hx.rmm.zpk"
      cvvKeyName: "CBS.CBS.CVK"
    esb:
      url: "http://***********:39503/CBS"
      serviceCode: "3003200002201"
      serviceCode1: "3003100001408"
    gl-system: "SMRATGL"

libra:
  configUrl: "libra-mysql"
  decrypt: "0"
  upright:
    url: {{ printf "%s/%s?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.rbDbHost .Values.rbDbName | quote }}
    user: {{ .Values.rbDbUser | quote }}
    password: {{ .Values.rbDbPassword | quote }}
  level1:
    url: {{ printf "%s/%s001?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.rbDbHost .Values.rbDbName | quote }}
    user: {{ .Values.rbDbUser | quote }}
    password: {{ .Values.rbDbPassword | quote }}
  level2:
    url: {{ printf "%s/%s002?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.rbDbHost .Values.rbDbName | quote }}
    user: {{ .Values.rbDbUser | quote }}
    password: {{ .Values.rbDbPassword | quote }}
  level3:
    url: {{ printf "%s/%s003?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.rbDbHost .Values.rbDbName | quote }}
    user: {{ .Values.rbDbUser | quote }}
    password: {{ .Values.rbDbPassword | quote }}
  level4:
    url: {{ printf "%s/%s004?characterEncoding=utf-8&useSSL=false&rewriteBatchedStatements=true" .Values.rbDbHost .Values.rbDbName | quote }}
    user: {{ .Values.rbDbUser | quote }}
    password: {{ .Values.rbDbPassword | quote }}
mybatis:
  config-location: "classpath:mybatis/mybatis-config-mysql.xml"

jasypt:
  encryptor:
    bean: "sm4CBCJasypt"
    password: "dcits"
    algorithm: "PBEWithMD5AndDES"
    salt-generator-classname: "org.jasypt.salt.RandomSaltGenerator"
    iv-generator-classname: "org.jasypt.iv.NoIvGenerator"

comet:
  tenant:
    tenant-id: "online-batch"
  esc-sdk:
    healthyTask:
      enabled: false
      isProtoHealthy: true
      period: 30
      report-times: 3
sftp:
  clients: []
ensemble:
  system:
    checkNull:
      enabled: false 