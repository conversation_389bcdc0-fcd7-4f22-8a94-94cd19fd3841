apiVersion: v1
kind: Service
metadata:
  name: {{ include "ensemble-tae-service.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "ensemble-tae-service.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "ensemble-tae-service.selectorLabels" . | nindent 4 }} 