ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

replicas: 1
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

image:
  repository: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/ensemble-tae-service"
  tag: "0.0.1-alpha"
  pullPolicy: "IfNotPresent"

configDirectory: "/data/app/config"
logDirectory: "/logs"

service:
  type: "ClusterIP"
  port: 8080
  targetPort: 8080

resources:
  limits:
    cpu: "500m"
    memory: "512Mi"
  requests:
    cpu: "50m"
    memory: "64Mi"

env:
  - name: "DB_HOST"
    value: "mysql-service"
  - name: "DB_PORT"
    value: "3306"

persistentVolume:
  enabled: false
  storageClass: ""
  accessMode: "ReadWriteOnce"
  size: "1Gi"
  mountPath: "/app/data"

volumeMounts:
  - name: "logs"
    mountPath: "/logs"

volumes:
  - name: "logs"
    hostPath:
      path: /home/<USER>/logs
      type: DirectoryOrCreate

livenessProbe:
  httpGet:
    path: "/health"
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: "/ready"
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5

# 服务专属变量
taeDbName: ens_tae
taeDbUser: root
taeDbPassword: 1q2w3e4R
taeDbHost: ****************************************************************************

# configMap 只保留 enabled 字段
configMap:
  enabled: true