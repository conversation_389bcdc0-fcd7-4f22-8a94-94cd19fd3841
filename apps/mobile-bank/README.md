# Mobile Bank 项目

Mobile Bank是一个基于微服务架构的银行核心系统，使用Kubernetes和Helm进行容器化部署。

## 项目结构

```
mobile-bank/
├── Chart.yaml                 # Helm Chart定义
├── values.yaml                # 全局配置文件
├── values-dev.yaml            # 开发环境配置
├── values-test.yaml           # 测试环境配置
├── values-prod.yaml           # 生产环境配置
├── charts/                    # 子服务Chart目录
│   └── fpaas-apigw-http/     # API网关服务
├── scripts/                   # 构建和部署脚本
│   ├── ci-cd.sh              # CI/CD Pipeline脚本
│   ├── build-images.sh       # Docker镜像构建脚本
│   └── health-check.sh       # 健康检查脚本
├── deploy.sh                  # 主部署脚本
├── Makefile                   # Make构建文件
└── README.md                  # 项目文档
```

## 快速开始

### 前置要求

- Docker
- Kubernetes集群
- Helm 3.x
- AWS CLI (用于ECR)
- kubectl

### 1. 环境准备

```bash
# 检查依赖
make check-deps

# 配置AWS认证
aws configure

# 配置kubectl
kubectl config use-context your-cluster-context
```

### 2. 构建和部署

#### 使用Make命令（推荐）

```bash
# 查看所有可用命令
make help

# 构建镜像
make build TAG=v1.0.0

# 部署到开发环境
make deploy-dev

# 部署到测试环境
make deploy-test

# 部署到生产环境
make deploy-prod

# 查看部署状态
make status

# 查看日志
make logs SERVICE=fpaas-apigw-http
```

#### 使用部署脚本

```bash
# 构建所有服务镜像
./deploy.sh build -t v1.0.0

# 推送镜像到ECR
./deploy.sh push -t v1.0.0

# 部署到开发环境
./deploy.sh deploy -n onebank-dev -e dev -t v1.0.0

# 查看部署状态
./deploy.sh status -n onebank-dev

# 查看服务日志
./deploy.sh logs -n onebank-dev -s fpaas-apigw-http
```

### 3. 健康检查

```bash
# 完整健康检查
./scripts/health-check.sh --full-check -n onebank-dev

# 等待Pod就绪
./scripts/health-check.sh --wait-ready -n onebank-dev

# 检查Service端点
./scripts/health-check.sh --check-endpoints -n onebank-dev
```

## 配置说明

### 全局配置 (values.yaml)

全局配置文件包含所有服务的公共配置：

- **命名空间配置**: 默认部署命名空间
- **镜像配置**: ECR仓库地址和拉取策略
- **数据库配置**: MySQL连接信息
- **中间件配置**: Redis、Nacos、Kafka等
- **资源配置**: CPU和内存限制
- **JVM配置**: Java虚拟机参数
- **安全配置**: Pod安全上下文

### 环境特定配置

- `values-dev.yaml`: 开发环境配置（资源较小，调试模式）
- `values-test.yaml`: 测试环境配置（中等资源，启用HPA）
- `values-prod.yaml`: 生产环境配置（完整资源，高可用）

### 服务特定配置

每个服务在`charts/服务名/values.yaml`中定义特有配置：

- 服务端口和探针
- Ingress配置
- 特定的环境变量
- 服务级别的资源限制

## 部署环境

### 开发环境 (dev)

- **命名空间**: `onebank-dev`
- **副本数**: 1
- **资源**: 较小配置
- **特性**: 启用调试模式，宽松的探针配置

### 测试环境 (test)

- **命名空间**: `onebank-test`
- **副本数**: 2
- **资源**: 中等配置
- **特性**: 启用HPA，标准探针配置

### 生产环境 (prod)

- **命名空间**: `onebank`
- **副本数**: 3+
- **资源**: 完整配置
- **特性**: 高可用、Pod反亲和性、PDB、网络策略

## CI/CD集成

### Jenkins Pipeline

```groovy
pipeline {
    agent any
    stages {
        stage('Build') {
            steps {
                sh './scripts/ci-cd.sh'
            }
        }
    }
}
```

### GitLab CI

```yaml
stages:
  - build
  - deploy

build:
  stage: build
  script:
    - ./scripts/ci-cd.sh
```

### GitHub Actions

```yaml
name: CI/CD
on: [push]
jobs:
  build-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - run: ./scripts/ci-cd.sh
```

## 监控和日志

### 查看Pod状态

```bash
kubectl get pods -n onebank -l app.kubernetes.io/instance=mobile-bank
```

### 查看服务日志

```bash
kubectl logs -n onebank -l app.kubernetes.io/name=fpaas-apigw-http --tail=100 -f
```

### 端口转发

```bash
kubectl port-forward -n onebank svc/fpaas-apigw-http 8080:8084
```

### 健康检查端点

- **健康检查**: `http://service:port/actuator/health`
- **指标**: `http://service:port/actuator/metrics`
- **信息**: `http://service:port/actuator/info`

## 故障排除

### 常见问题

1. **镜像拉取失败**
   ```bash
   # 检查ECR登录
   aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-southeast-1.amazonaws.com
   ```

2. **Pod启动失败**
   ```bash
   # 查看Pod事件
   kubectl describe pod <pod-name> -n onebank
   
   # 查看Pod日志
   kubectl logs <pod-name> -n onebank
   ```

3. **Service无法访问**
   ```bash
   # 检查Service端点
   kubectl get endpoints <service-name> -n onebank
   
   # 检查网络策略
   kubectl get networkpolicy -n onebank
   ```

### 调试命令

```bash
# 进入Pod调试
kubectl exec -it <pod-name> -n onebank -- /bin/bash

# 运行临时调试Pod
kubectl run debug --image=busybox -it --rm --restart=Never -- sh

# 检查DNS解析
kubectl run nslookup --image=busybox -it --rm --restart=Never -- nslookup <service-name>
```

## 安全注意事项

1. **镜像安全**: 定期扫描镜像漏洞
2. **密钥管理**: 使用Kubernetes Secret管理敏感信息
3. **网络安全**: 配置网络策略限制Pod间通信
4. **RBAC**: 配置适当的角色和权限
5. **Pod安全**: 使用非root用户运行容器

## 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。

## 联系方式

- 项目维护者: DevOps Team
- 邮箱: <EMAIL>
- 文档: https://docs.yourcompany.com/mobile-bank
