# Mobile Bank 开发环境配置
# 继承全局配置，覆盖开发环境特定设置

global:
  # 开发环境命名空间
  namespace: "onebank-dev"
  
  # 开发环境配置
  config:
    profile: "dev"
    
  # 开发环境资源配置（较小）
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "64Mi"

  # 开发环境JVM配置（较小）
  jvm:
    xms: "512m"
    xmx: "512m"
    xmn: "128m"
    metaspaceSize: "256m"
    maxMetaspaceSize: "512m"

  # 开发环境数据库配置
  mysql:
    host: "onebankmysql-dev.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com"
    database: "fpaas_dev"

  # 开发环境Redis配置
  redis:
    host: "redis-standalone-redis.paas.svc.cluster.local"
    database: 1

  # 开发环境Nacos配置
  nacos:
    host: "nacos-standalone-nacos.paas.svc.cluster.local"
    namespace: "dev"

# 服务特定配置
fpaas-apigw-http:
  enabled: true
  replicaCount: 1
  
  # 开发环境启用调试
  jvm:
    additionalOpts: "-Ddebug=true -Dspring.profiles.active=dev"
  
  # 开发环境Ingress配置
  ingress:
    enabled: true
    className: "alb"
    annotations:
      kubernetes.io/ingress.class: alb
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/target-type: ip
    hosts:
      - host: fpaas-apigw-dev.internal.com
        paths:
          - path: /
            pathType: Prefix

  # 开发环境探针配置（更宽松）
  livenessProbe:
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 5

  readinessProbe:
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 5
