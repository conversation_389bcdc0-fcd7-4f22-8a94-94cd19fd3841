# Mobile Bank 生产环境配置
# 继承全局配置，覆盖生产环境特定设置

global:
  # 生产环境命名空间
  namespace: "onebank"
  
  # 生产环境配置
  config:
    profile: "prod"
    
  # 生产环境资源配置（完整）
  resources:
    limits:
      cpu: "2000m"
      memory: "2Gi"
    requests:
      cpu: "200m"
      memory: "256Mi"

  # 生产环境JVM配置（完整）
  jvm:
    xms: "1536m"
    xmx: "1536m"
    xmn: "384m"
    metaspaceSize: "768m"
    maxMetaspaceSize: "1536m"

  # 生产环境数据库配置
  mysql:
    host: "onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com"
    database: "fpaas"

  # 生产环境Redis配置
  redis:
    host: "redis-standalone-redis.paas.svc.cluster.local"
    database: 0
    ssl: true  # 生产环境启用SSL

  # 生产环境Nacos配置
  nacos:
    host: "nacos-standalone-nacos.paas.svc.cluster.local"
    namespace: "prod"

  # 生产环境持久化配置
  persistence:
    enabled: true
    storageClass: "gp3"  # 使用更高性能的存储
    size: 50Gi

# 服务特定配置
fpaas-apigw-http:
  enabled: true
  replicaCount: 3
  
  # 生产环境启用自动扩缩容
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 60
    targetMemoryUtilizationPercentage: 70
  
  # 生产环境Ingress配置
  ingress:
    enabled: true
    className: "alb"
    annotations:
      kubernetes.io/ingress.class: alb
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/healthcheck-path: /actuator/health
      alb.ingress.kubernetes.io/ssl-redirect: "443"
      alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-southeast-1:164104811622:certificate/your-cert-arn"
    hosts:
      - host: fpaas-apigw.yourdomain.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: fpaas-apigw-tls
        hosts:
          - fpaas-apigw.yourdomain.com

  # 生产环境探针配置（严格）
  livenessProbe:
    initialDelaySeconds: 120
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3

  readinessProbe:
    initialDelaySeconds: 60
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

  # 生产环境Pod反亲和性
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          podAffinityTerm:
            labelSelector:
              matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                    - fpaas-apigw-http
            topologyKey: kubernetes.io/hostname

  # 生产环境Pod中断预算
  podDisruptionBudget:
    enabled: true
    minAvailable: 2

  # 生产环境网络策略
  networkPolicy:
    enabled: true
    ingress:
      - from:
          - namespaceSelector:
              matchLabels:
                name: ingress-nginx
        ports:
          - protocol: TCP
            port: 8084
