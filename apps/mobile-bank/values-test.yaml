# Mobile Bank 测试环境配置
# 继承全局配置，覆盖测试环境特定设置

global:
  # 测试环境命名空间
  namespace: "onebank-test"
  
  # 测试环境配置
  config:
    profile: "test"
    
  # 测试环境资源配置（中等）
  resources:
    limits:
      cpu: "750m"
      memory: "768Mi"
    requests:
      cpu: "75m"
      memory: "96Mi"

  # 测试环境JVM配置（中等）
  jvm:
    xms: "768m"
    xmx: "768m"
    xmn: "192m"
    metaspaceSize: "384m"
    maxMetaspaceSize: "768m"

  # 测试环境数据库配置
  mysql:
    host: "onebankmysql-test.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com"
    database: "fpaas_test"

  # 测试环境Redis配置
  redis:
    host: "redis-standalone-redis.paas.svc.cluster.local"
    database: 2

  # 测试环境Nacos配置
  nacos:
    host: "nacos-standalone-nacos.paas.svc.cluster.local"
    namespace: "test"

# 服务特定配置
fpaas-apigw-http:
  enabled: true
  replicaCount: 2
  
  # 测试环境启用自动扩缩容
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 4
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  # 测试环境Ingress配置
  ingress:
    enabled: true
    className: "alb"
    annotations:
      kubernetes.io/ingress.class: alb
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/healthcheck-path: /actuator/health
    hosts:
      - host: fpaas-apigw-test.internal.com
        paths:
          - path: /
            pathType: Prefix

  # 测试环境探针配置（标准）
  livenessProbe:
    initialDelaySeconds: 90
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3

  readinessProbe:
    initialDelaySeconds: 45
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
