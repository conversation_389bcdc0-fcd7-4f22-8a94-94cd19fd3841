global:
  # 全局命名空间配置
  namespace: "onebank"

  # 全局镜像配置
  image:
    registry: "************.dkr.ecr.ap-southeast-1.amazonaws.com"
    repository: "onebank"
    pullPolicy: "IfNotPresent"

  # 初始化容器镜像
  initImage:
    repository: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox"
    tag: "latest"
    pullPolicy: "IfNotPresent"

  # 全局数据库配置
  mysql:
    host: "mysql-db15fbdc1.cro2440qi0n2.ap-southeast-1.rds.amazonaws.com"
    port: 3306
    database: "fpaas"
    username: "root"
    password: "rootpasswd#45r"

  # 全局Redis配置
  redis:
    database: 0
    host: "redis-standalone-redis.paas.svc.cluster.local"
    port: 6379
    password: ""
    ssl: false

  # 全局Nacos配置
  nacos:
    host: "nacos-standalone-nacos.paas.svc.cluster.local"
    port: 8848
    namespace: "public"

  # 全局Kafka配置
  kafka:
    host: "kafka-host"
    port: 9092

  # 全局持久化存储配置
  persistence:
    enabled: false  # 暂时禁用持久化存储
    storageClass: "gp2"  # AWS EKS默认存储类
    accessMode: ReadWriteOnce
    size: 10Gi
    mountPath: /app/logs

  # 全局应用配置
  config:
    tokenRefreshTime: 30000
    tokenTimeout: *********
    httpMaxSize: "********"
    profile: "fat"  # 默认环境配置

  # 全局资源配置
  resources:
    limits:
      cpu: "1000m"
      memory: "1Gi"
    requests:
      cpu: "100m"
      memory: "128Mi"

  # 全局JVM配置
  jvm:
    xms: "1024m"
    xmx: "1024m"
    xmn: "256m"
    metaspaceSize: "512m"
    maxMetaspaceSize: "1024m"

  # 全局安全上下文
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000

  # 全局Pod安全上下文
  podSecurityContext:
    fsGroup: 1000

# 服务特定配置
fpaas-apigw-http:
  enabled: true