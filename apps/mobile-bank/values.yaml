global:
  namespace: "onebank"

  image:
    host: "************.dkr.ecr.ap-southeast-1.amazonaws.com"
    repository: "onebank"
    pullPolicy: "IfNotPresent"
    
  initImage:
      repository: "{{ .Values.global.image.host }}/{{ .Values.global.image.repository }}/busybox"
      tag: "latest"
      pullPolicy: "IfNotPresent"

  mysql:
    host: "onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com"
    port: 3306
    database: "fpaas"
    username: "fpaas"
    password: "1q2w3e4R!@#$"

  redis:
    database: 0
    host: "redis-standalone-redis.paas"
    port: 6379
    password: ""

  nacos:
    host: "nacos-standalone-nacos.paas"
    port: 8848

  kafka:
    host: "kafka-host"
    port: 8092

  # Persistent Volume for logs
  persistence:
    enabled: true
    storageClass: ""
    accessMode: ReadWriteOnce
    size: 10Gi
    mountPath: /app/logs

  # ConfigMap data
  config:
    # Additional application properties can be added here
    tokenRefreshTime: 30000
    tokenTimeout: *********
    httpMaxSize: ********

fpaas-apigw-http:
  enabled: true