#!/bin/bash

# Mobile Bank 项目构建和部署脚本
# 作者: DevOps Team
# 版本: 1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="mobile-bank"
CHART_NAME="mobile-bank"
DEFAULT_NAMESPACE="onebank"
DEFAULT_ENVIRONMENT="dev"
DEFAULT_REGION="ap-southeast-1"

# AWS ECR配置
ECR_REGISTRY="************.dkr.ecr.ap-southeast-1.amazonaws.com"
ECR_REPOSITORY="onebank"

# 帮助信息
show_help() {
    cat << EOF
Mobile Bank 部署脚本

用法: $0 [选项] [命令]

命令:
    build           构建所有服务的Docker镜像
    push            推送镜像到ECR
    deploy          部署到Kubernetes集群
    upgrade         升级现有部署
    rollback        回滚到上一个版本
    status          查看部署状态
    logs            查看服务日志
    clean           清理资源
    help            显示此帮助信息

选项:
    -n, --namespace NAMESPACE    Kubernetes命名空间 (默认: $DEFAULT_NAMESPACE)
    -e, --environment ENV        部署环境 (dev/test/prod) (默认: $DEFAULT_ENVIRONMENT)
    -s, --service SERVICE        指定服务名称 (默认: 所有服务)
    -t, --tag TAG               镜像标签 (默认: latest)
    -r, --region REGION         AWS区域 (默认: $DEFAULT_REGION)
    -v, --verbose               详细输出
    -h, --help                  显示帮助信息

示例:
    $0 build -t v1.0.0                    # 构建镜像并打标签v1.0.0
    $0 deploy -n onebank -e prod          # 部署到生产环境
    $0 upgrade -s fpaas-apigw-http        # 升级指定服务
    $0 logs -s fpaas-apigw-http           # 查看服务日志

EOF
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    local deps=("docker" "kubectl" "helm" "aws")
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null; then
            log_error "$dep 未安装或不在PATH中"
            exit 1
        fi
    done
    
    log_success "所有依赖工具检查通过"
}

# 检查AWS认证
check_aws_auth() {
    log_info "检查AWS认证..."
    
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS认证失败，请运行 aws configure 或设置相关环境变量"
        exit 1
    fi
    
    log_success "AWS认证检查通过"
}

# 登录ECR
ecr_login() {
    log_info "登录AWS ECR..."
    
    aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
    
    if [ $? -eq 0 ]; then
        log_success "ECR登录成功"
    else
        log_error "ECR登录失败"
        exit 1
    fi
}

# 获取服务列表
get_services() {
    if [ -n "$SERVICE" ]; then
        echo "$SERVICE"
    else
        # 从charts目录获取所有服务
        find "$SCRIPT_DIR/charts" -maxdepth 1 -type d -exec basename {} \; | grep -v "^charts$" | sort
    fi
}

# 构建Docker镜像
build_images() {
    log_info "开始构建Docker镜像..."
    
    local services=$(get_services)
    
    for service in $services; do
        log_info "构建服务: $service"
        
        local image_name="$ECR_REGISTRY/$ECR_REPOSITORY/$service:$TAG"
        
        # 检查是否存在Dockerfile
        if [ ! -f "$SCRIPT_DIR/charts/$service/Dockerfile" ]; then
            log_warning "服务 $service 没有找到Dockerfile，跳过构建"
            continue
        fi
        
        # 构建镜像
        docker build -t $image_name "$SCRIPT_DIR/charts/$service/"
        
        if [ $? -eq 0 ]; then
            log_success "服务 $service 镜像构建成功: $image_name"
        else
            log_error "服务 $service 镜像构建失败"
            exit 1
        fi
    done
}

# 推送镜像到ECR
push_images() {
    log_info "开始推送镜像到ECR..."
    
    local services=$(get_services)
    
    for service in $services; do
        local image_name="$ECR_REGISTRY/$ECR_REPOSITORY/$service:$TAG"
        
        # 检查镜像是否存在
        if ! docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "$image_name"; then
            log_warning "镜像 $image_name 不存在，跳过推送"
            continue
        fi
        
        log_info "推送镜像: $image_name"
        docker push $image_name
        
        if [ $? -eq 0 ]; then
            log_success "镜像推送成功: $image_name"
        else
            log_error "镜像推送失败: $image_name"
            exit 1
        fi
    done
}

# 部署到Kubernetes
deploy_to_k8s() {
    log_info "开始部署到Kubernetes集群..."
    
    # 检查命名空间是否存在
    if ! kubectl get namespace $NAMESPACE &> /dev/null; then
        log_info "创建命名空间: $NAMESPACE"
        kubectl create namespace $NAMESPACE
    fi
    
    # 更新Helm依赖
    log_info "更新Helm依赖..."
    helm dependency update "$SCRIPT_DIR"
    
    # 部署或升级
    log_info "部署应用到命名空间: $NAMESPACE"
    
    helm upgrade --install $CHART_NAME "$SCRIPT_DIR" \
        --namespace $NAMESPACE \
        --set global.image.tag=$TAG \
        --set global.environment=$ENVIRONMENT \
        --timeout 10m \
        --wait
    
    if [ $? -eq 0 ]; then
        log_success "部署成功"
    else
        log_error "部署失败"
        exit 1
    fi
}

# 查看部署状态
show_status() {
    log_info "查看部署状态..."
    
    echo "=== Helm Release 状态 ==="
    helm status $CHART_NAME -n $NAMESPACE
    
    echo -e "\n=== Pod 状态 ==="
    kubectl get pods -n $NAMESPACE -l app.kubernetes.io/instance=$CHART_NAME
    
    echo -e "\n=== Service 状态 ==="
    kubectl get svc -n $NAMESPACE -l app.kubernetes.io/instance=$CHART_NAME
    
    echo -e "\n=== Ingress 状态 ==="
    kubectl get ingress -n $NAMESPACE -l app.kubernetes.io/instance=$CHART_NAME
}

# 查看日志
show_logs() {
    if [ -n "$SERVICE" ]; then
        log_info "查看服务 $SERVICE 的日志..."
        kubectl logs -n $NAMESPACE -l app.kubernetes.io/name=$SERVICE --tail=100 -f
    else
        log_info "查看所有服务的日志..."
        kubectl logs -n $NAMESPACE -l app.kubernetes.io/instance=$CHART_NAME --tail=100
    fi
}

# 回滚部署
rollback_deployment() {
    log_info "回滚部署..."
    
    helm rollback $CHART_NAME -n $NAMESPACE
    
    if [ $? -eq 0 ]; then
        log_success "回滚成功"
    else
        log_error "回滚失败"
        exit 1
    fi
}

# 清理资源
clean_resources() {
    log_warning "这将删除所有相关资源，请确认是否继续？(y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "清理资源..."
        helm uninstall $CHART_NAME -n $NAMESPACE
        log_success "资源清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -s|--service)
                SERVICE="$2"
                shift 2
                ;;
            -t|--tag)
                TAG="$2"
                shift 2
                ;;
            -r|--region)
                REGION="$2"
                shift 2
                ;;
            -v|--verbose)
                set -x
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            build|push|deploy|upgrade|rollback|status|logs|clean|help)
                COMMAND="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 设置默认值
    NAMESPACE=${NAMESPACE:-$DEFAULT_NAMESPACE}
    ENVIRONMENT=${ENVIRONMENT:-$DEFAULT_ENVIRONMENT}
    REGION=${REGION:-$DEFAULT_REGION}
    TAG=${TAG:-"latest"}
    
    # 检查命令
    if [ -z "$COMMAND" ]; then
        log_error "请指定命令"
        show_help
        exit 1
    fi
    
    # 执行命令
    case $COMMAND in
        build)
            check_dependencies
            check_aws_auth
            ecr_login
            build_images
            ;;
        push)
            check_dependencies
            check_aws_auth
            ecr_login
            push_images
            ;;
        deploy)
            check_dependencies
            deploy_to_k8s
            ;;
        upgrade)
            check_dependencies
            deploy_to_k8s
            ;;
        rollback)
            check_dependencies
            rollback_deployment
            ;;
        status)
            check_dependencies
            show_status
            ;;
        logs)
            check_dependencies
            show_logs
            ;;
        clean)
            check_dependencies
            clean_resources
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 解析参数并执行主函数
parse_args "$@"
main
