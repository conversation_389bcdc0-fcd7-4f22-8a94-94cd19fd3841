{{/*
Expand the name of the chart.
*/}}
{{- define "fpaas-file-upload.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "fpaas-file-upload.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "fpaas-file-upload.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "fpaas-file-upload.labels" -}}
helm.sh/chart: {{ include "fpaas-file-upload.chart" . }}
{{ include "fpaas-file-upload.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/part-of: fpaas
app.kubernetes.io/component: file-upload
{{- end }}

{{/*
Selector labels
*/}}
{{- define "fpaas-file-upload.selectorLabels" -}}
app.kubernetes.io/name: {{ include "fpaas-file-upload.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "fpaas-file-upload.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "fpaas-file-upload.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the image name
*/}}
{{- define "fpaas-file-upload.image" -}}
{{- $registry := .Values.global.imageRegistry | default .Values.image.registry -}}
{{- $repository := .Values.image.repository -}}
{{- $tag := .Values.image.tag | default .Chart.AppVersion -}}
{{- if $registry }}
{{- printf "%s/%s:%s" $registry $repository $tag }}
{{- else }}
{{- printf "%s:%s" $repository $tag }}
{{- end }}
{{- end }}

{{/*
Create database URL
*/}}
{{- define "fpaas-file-upload.databaseUrl" -}}
{{- printf "**************************************************************************************************************************************" .Values.database.host (.Values.database.port | int) .Values.database.name }}
{{- end }}

{{/*
Create MinIO endpoint URL
*/}}
{{- define "fpaas-file-upload.minioEndpoint" -}}
{{- if hasPrefix "http" .Values.minio.endpoint }}
{{- .Values.minio.endpoint }}
{{- else }}
{{- printf "http://%s" .Values.minio.endpoint }}
{{- end }}
{{- end }}

{{/*
Create Nacos server address
*/}}
{{- define "fpaas-file-upload.nacosServerAddr" -}}
{{- printf "%s:8848" .Values.nacos.serverAddr }}
{{- end }}

{{/*
Create Consul server address
*/}}
{{- define "fpaas-file-upload.consulServerAddr" -}}
{{- printf "%s:%d" .Values.consul.host (.Values.consul.port | int) }}
{{- end }}

{{/*
JVM Options
*/}}
{{- define "fpaas-file-upload.jvmOpts" -}}
{{- printf "-server -Xms%s -Xmx%s -Xmn%s -XX:MetaspaceSize=%s -XX:MaxMetaspaceSize=%s -XX:-OmitStackTraceInFastThrow" .Values.jvm.xms .Values.jvm.xmx .Values.jvm.xmn .Values.jvm.metaspaceSize .Values.jvm.maxMetaspaceSize }}
{{- end }}

{{/*
Environment name for configuration
*/}}
{{- define "fpaas-file-upload.environment" -}}
{{- .Values.environment | default "dev" }}
{{- end }}
