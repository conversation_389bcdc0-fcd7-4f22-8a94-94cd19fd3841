{{- if and .Values.monitoring.enabled .Values.monitoring.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "fpaas-file-upload.fullname" . }}
  {{- if .Values.monitoring.serviceMonitor.namespace }}
  namespace: {{ .Values.monitoring.serviceMonitor.namespace }}
  {{- end }}
  labels:
    {{- include "fpaas-file-upload.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "fpaas-file-upload.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: metrics
  endpoints:
    - port: metrics
      path: {{ .Values.monitoring.serviceMonitor.path }}
      interval: {{ .Values.monitoring.serviceMonitor.interval }}
      scrapeTimeout: 10s
{{- end }}
