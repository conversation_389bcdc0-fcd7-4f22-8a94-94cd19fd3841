# FPAAS File Upload Service - He<PERSON> Chart

This Helm chart deploys the FPAAS File Upload Service to Kubernetes, specifically optimized for AWS EKS.

## Prerequisites

- Kubernetes 1.19+
- Helm 3.0+
- AWS EKS cluster
- MySQL database
- MinIO object storage
- (Optional) Nacos service discovery
- (Optional) Consul service discovery

## Installation

### 1. Build and Push Docker Image

First, build and push the Docker image to your container registry:

```bash
# Build the Docker image
docker build -t your-registry/fpaas-file-upload:2.6.0 .

# Push to your registry
docker push your-registry/fpaas-file-upload:2.6.0
```

### 2. Install the Helm Chart

#### Development Environment

```bash
helm install fpaas-file-upload ./helm/fpaas-file-upload \
  -f ./helm/fpaas-file-upload/values-dev.yaml \
  --namespace fpaas-dev \
  --create-namespace \
  --set image.registry=your-registry \
  --set database.password=your-dev-db-password \
  --set minio.secretKey=your-dev-minio-secret
```

#### Staging Environment

```bash
helm install fpaas-file-upload ./helm/fpaas-file-upload \
  -f ./helm/fpaas-file-upload/values-staging.yaml \
  --namespace fpaas-staging \
  --create-namespace \
  --set image.registry=your-registry \
  --set database.password=your-staging-db-password \
  --set minio.secretKey=your-staging-minio-secret
```

#### Production Environment

```bash
helm install fpaas-file-upload ./helm/fpaas-file-upload \
  -f ./helm/fpaas-file-upload/values-prod.yaml \
  --namespace fpaas-prod \
  --create-namespace \
  --set image.registry=your-registry \
  --set database.password=your-prod-db-password \
  --set minio.secretKey=your-prod-minio-secret
```

## Configuration

### Key Configuration Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `image.registry` | Container registry | `""` |
| `image.repository` | Image repository | `fpaas-file-upload` |
| `image.tag` | Image tag | `2.6.0` |
| `replicaCount` | Number of replicas | `2` |
| `database.host` | Database host | `""` |
| `database.password` | Database password | `changeme` |
| `minio.endpoint` | MinIO endpoint | `""` |
| `minio.secretKey` | MinIO secret key | `minioadmin` |
| `fileUpload.encryptType` | File encryption type | `SM4` |
| `ingress.enabled` | Enable ingress | `false` |

### Environment Variables

The application supports configuration through environment variables. Key variables include:

- `SPRING_DATASOURCE_URL`: Database connection URL
- `SPRING_DATASOURCE_PASSWORD`: Database password
- `EC_OSS_MINIO_ENDPOINT`: MinIO endpoint
- `EC_OSS_MINIO_SECRET_KEY`: MinIO secret key
- `FPAAS_FILE_UPLOAD_ENCRYPT_TYPE`: File encryption type

## Health Checks

The application provides health check endpoints:

- **Liveness Probe**: `/actuator/health/liveness`
- **Readiness Probe**: `/actuator/health/readiness`
- **General Health**: `/actuator/health`

## Monitoring

The chart includes support for Prometheus monitoring:

- Metrics endpoint: `/actuator/prometheus`
- ServiceMonitor for Prometheus Operator (optional)

## Persistence

The chart supports persistent storage for temporary files:

- **Mount Path**: `/tmp/fpaas-temp`
- **Default Size**: `10Gi`
- **Storage Class**: Configurable (defaults to cluster default)

## Security

### Security Context

The application runs as a non-root user (UID 1000) with the following security context:

```yaml
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000
```

### Secrets Management

Sensitive data is stored in Kubernetes secrets:

- Database passwords
- MinIO secret keys
- SM4 encryption keys
- Authentication URLs

## Scaling

### Manual Scaling

```bash
kubectl scale deployment fpaas-file-upload --replicas=5 -n fpaas-prod
```

### Horizontal Pod Autoscaler

Enable autoscaling in values file:

```yaml
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
```

## Troubleshooting

### Common Issues

1. **Pod not starting**: Check resource limits and node capacity
2. **Database connection issues**: Verify database credentials and network connectivity
3. **MinIO connection issues**: Check MinIO endpoint and credentials
4. **File upload failures**: Verify persistent volume and permissions

### Useful Commands

```bash
# Check pod status
kubectl get pods -n fpaas-prod

# View pod logs
kubectl logs -f deployment/fpaas-file-upload -n fpaas-prod

# Check service endpoints
kubectl get endpoints -n fpaas-prod

# Describe pod for events
kubectl describe pod <pod-name> -n fpaas-prod

# Check persistent volume claims
kubectl get pvc -n fpaas-prod
```

## Upgrading

```bash
# Upgrade to new version
helm upgrade fpaas-file-upload ./helm/fpaas-file-upload \
  -f ./helm/fpaas-file-upload/values-prod.yaml \
  --namespace fpaas-prod \
  --set image.tag=2.7.0
```

## Uninstalling

```bash
helm uninstall fpaas-file-upload --namespace fpaas-prod
```

## Support

For issues and questions, please contact the Platform <NAME_EMAIL>.
