{{- if .Values.global.persistence.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "fpaas-apigw-http.fullname" . }}-pvc
  labels:
    {{- include "fpaas-apigw-http.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.global.persistence.accessMode }}
  resources:
    requests:
      storage: {{ .Values.global.persistence.size }}
  {{- if .Values.global.persistence.storageClass }}
  {{- if (eq "-" .Values.global.persistence.storageClass) }}
  storageClassName: ""
  {{- else }}
  storageClassName: {{ .Values.global.persistence.storageClass }}
  {{- end }}
  {{- end }}
{{- end }}
