apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "fpaas-apigw-http.fullname" . }}
  labels:
    {{- include "fpaas-apigw-http.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "fpaas-apigw-http.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "fpaas-apigw-http.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "fpaas-apigw-http.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.global.image.registry }}/{{ .Values.global.image.repository }}/fpaas-apigw-http:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: {{ .Values.app.profile }}
            - name: JAVA_OPTS
              value: >-
                -server
                -Xms{{ .Values.jvm.xms }}
                -Xmx{{ .Values.jvm.xmx }}
                -Xmn{{ .Values.jvm.xmn }}
                -XX:MetaspaceSize={{ .Values.jvm.metaspaceSize }}
                -XX:MaxMetaspaceSize={{ .Values.jvm.maxMetaspaceSize }}
                -XX:-OmitStackTraceInFastThrow
            - name: DATASOURCE_HOST
              value: "{{ .Values.global.mysql.host }}:{{ .Values.global.mysql.port }}"
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "fpaas-apigw-http.fullname" . }}-secret
                  key: mysql-password
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "fpaas-apigw-http.fullname" . }}-secret
                  key: redis-password
            - name: LOGGING_CONFIG
              value: "file:/app/config/logback-spring.xml"
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: config-volume
              mountPath: /app/config
              readOnly: true
            {{- if .Values.global.persistence.enabled }}
            - name: logs-volume
              mountPath: {{ .Values.global.persistence.mountPath }}
            {{- end }}
      volumes:
        - name: config-volume
          configMap:
            name: {{ include "fpaas-apigw-http.fullname" . }}-config
        {{- if .Values.global.persistence.enabled }}
        - name: logs-volume
          persistentVolumeClaim:
            claimName: {{ include "fpaas-apigw-http.fullname" . }}-pvc
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
