apiVersion: v1
kind: Secret
metadata:
  name: {{ include "fpaas-apigw-http.fullname" . }}-secret
  labels:
    {{- include "fpaas-apigw-http.labels" . | nindent 4 }}
type: Opaque
data:
  mysql-password: {{ .Values.global.mysql.password | b64enc }}
  {{- if .Values.global.redis.password }}
  redis-password: {{ .Values.global.redis.password | b64enc }}
  {{- else }}
  redis-password: {{ "" | b64enc }}
  {{- end }}
