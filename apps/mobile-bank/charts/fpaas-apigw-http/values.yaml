# Default values for fpaas-apigw-http
# This is a YAML-formatted file.

# Application configuration
app:
  name: fpaas-apigw-http
  version: "2.6.1"
  profile: fat  # Using fat profile as requested

# Image configuration
image:
  repository: "{{ .Values.global.image.host }}/{{ .Values.global.image.repository }}/fpaas-apigw-http"
  pullPolicy: IfNotPresent
  tag: "0.0.1-alpha"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

# Service Account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod Security Context
podSecurityContext:
  fsGroup: 1000

# Container Security Context
securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: 1000

# Service configuration
service:
  type: ClusterIP
  port: 8084
  targetPort: 8084
  annotations: {}

# Ingress configuration
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: fpaas-apigw.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Resource limits and requests
resources:
  limits:
    cpu: "1000m"
    memory: "1Gi"
  requests:
    cpu: "100m"
    memory: "128Mi"

# JVM configuration
jvm:
  xms: "1024m"
  xmx: "1024m"
  xmn: "256m"
  metaspaceSize: "512m"
  maxMetaspaceSize: "1024m"

# Autoscaling
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Pod configuration
replicaCount: 1
podAnnotations: {}
podLabels: {}

# Node selection
nodeSelector: {}
tolerations: []
affinity: {}

# Probes configuration
livenessProbe:
  httpGet:
    path: /actuator/health
    port: 8084
  initialDelaySeconds: 120
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health
    port: 8084
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

