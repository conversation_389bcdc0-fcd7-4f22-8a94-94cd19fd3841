#!/bin/bash

# Mobile Bank Docker镜像构建脚本
# 支持多架构构建和并行构建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_CONTEXT="$PROJECT_DIR"

# 默认配置
DEFAULT_REGISTRY="************.dkr.ecr.ap-southeast-1.amazonaws.com"
DEFAULT_REPOSITORY="onebank"
DEFAULT_TAG="latest"
DEFAULT_PLATFORM="linux/amd64"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 帮助信息
show_help() {
    cat << EOF
Mobile Bank Docker镜像构建脚本

用法: $0 [选项] [服务名...]

选项:
    -r, --registry REGISTRY     Docker镜像仓库地址 (默认: $DEFAULT_REGISTRY)
    -p, --repository REPO       仓库名称 (默认: $DEFAULT_REPOSITORY)
    -t, --tag TAG              镜像标签 (默认: $DEFAULT_TAG)
    -a, --platform PLATFORM    目标平台 (默认: $DEFAULT_PLATFORM)
    --multi-arch               启用多架构构建
    --push                     构建后自动推送
    --no-cache                 不使用缓存构建
    --parallel                 并行构建
    -v, --verbose              详细输出
    -h, --help                 显示帮助信息

示例:
    $0 fpaas-apigw-http                    # 构建单个服务
    $0 --push --tag v1.0.0                # 构建所有服务并推送
    $0 --multi-arch --platform linux/amd64,linux/arm64  # 多架构构建

EOF
}

# 获取服务列表
get_services() {
    if [ $# -gt 0 ]; then
        echo "$@"
    else
        # 从charts目录获取所有包含Dockerfile的服务
        find "$PROJECT_DIR/charts" -name "Dockerfile" -exec dirname {} \; | xargs -I {} basename {} | sort
    fi
}

# 检查Docker Buildx
check_buildx() {
    if ! docker buildx version &> /dev/null; then
        log_error "Docker Buildx未安装或不可用"
        exit 1
    fi
    
    # 创建或使用buildx builder
    if ! docker buildx inspect mobile-bank-builder &> /dev/null; then
        log_info "创建Docker Buildx builder..."
        docker buildx create --name mobile-bank-builder --use
    else
        docker buildx use mobile-bank-builder
    fi
}

# 构建单个服务
build_service() {
    local service=$1
    local dockerfile_path="$PROJECT_DIR/charts/$service/Dockerfile"
    local context_path="$PROJECT_DIR/charts/$service"
    
    if [ ! -f "$dockerfile_path" ]; then
        log_warning "服务 $service 没有找到Dockerfile，跳过构建"
        return 0
    fi
    
    local image_name="$REGISTRY/$REPOSITORY/$service:$TAG"
    log_info "构建服务: $service -> $image_name"
    
    # 构建参数
    local build_args=(
        "--file" "$dockerfile_path"
        "--tag" "$image_name"
    )
    
    # 添加平台参数
    if [ -n "$PLATFORM" ]; then
        build_args+=("--platform" "$PLATFORM")
    fi
    
    # 添加推送参数
    if [ "$PUSH" = "true" ]; then
        build_args+=("--push")
    else
        build_args+=("--load")
    fi
    
    # 添加缓存参数
    if [ "$NO_CACHE" = "true" ]; then
        build_args+=("--no-cache")
    fi
    
    # 添加构建参数
    build_args+=(
        "--build-arg" "BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')"
        "--build-arg" "VCS_REF=$(git rev-parse --short HEAD)"
        "--build-arg" "VERSION=$TAG"
    )
    
    # 执行构建
    if [ "$VERBOSE" = "true" ]; then
        docker buildx build "${build_args[@]}" "$context_path"
    else
        docker buildx build "${build_args[@]}" "$context_path" > /dev/null 2>&1
    fi
    
    if [ $? -eq 0 ]; then
        log_success "服务 $service 构建成功"
        return 0
    else
        log_error "服务 $service 构建失败"
        return 1
    fi
}

# 并行构建
build_parallel() {
    local services=("$@")
    local pids=()
    local failed_services=()
    
    log_info "开始并行构建 ${#services[@]} 个服务..."
    
    # 启动并行构建
    for service in "${services[@]}"; do
        build_service "$service" &
        pids+=($!)
    done
    
    # 等待所有构建完成
    local index=0
    for pid in "${pids[@]}"; do
        if ! wait $pid; then
            failed_services+=("${services[$index]}")
        fi
        ((index++))
    done
    
    # 检查结果
    if [ ${#failed_services[@]} -eq 0 ]; then
        log_success "所有服务构建成功"
        return 0
    else
        log_error "以下服务构建失败: ${failed_services[*]}"
        return 1
    fi
}

# 顺序构建
build_sequential() {
    local services=("$@")
    local failed_services=()
    
    log_info "开始顺序构建 ${#services[@]} 个服务..."
    
    for service in "${services[@]}"; do
        if ! build_service "$service"; then
            failed_services+=("$service")
        fi
    done
    
    # 检查结果
    if [ ${#failed_services[@]} -eq 0 ]; then
        log_success "所有服务构建成功"
        return 0
    else
        log_error "以下服务构建失败: ${failed_services[*]}"
        return 1
    fi
}

# 清理构建缓存
cleanup_cache() {
    log_info "清理构建缓存..."
    docker buildx prune -f
    log_success "构建缓存清理完成"
}

# 显示构建摘要
show_summary() {
    local services=("$@")
    
    echo ""
    log_info "构建摘要:"
    echo "  项目: Mobile Bank"
    echo "  仓库: $REGISTRY/$REPOSITORY"
    echo "  标签: $TAG"
    echo "  平台: $PLATFORM"
    echo "  服务数量: ${#services[@]}"
    echo "  服务列表: ${services[*]}"
    echo "  推送: $([ "$PUSH" = "true" ] && echo "是" || echo "否")"
    echo "  并行构建: $([ "$PARALLEL" = "true" ] && echo "是" || echo "否")"
    echo "  多架构: $([ "$MULTI_ARCH" = "true" ] && echo "是" || echo "否")"
    echo ""
}

# 主函数
main() {
    # 设置默认值
    REGISTRY=${REGISTRY:-$DEFAULT_REGISTRY}
    REPOSITORY=${REPOSITORY:-$DEFAULT_REPOSITORY}
    TAG=${TAG:-$DEFAULT_TAG}
    PLATFORM=${PLATFORM:-$DEFAULT_PLATFORM}
    PUSH=${PUSH:-false}
    NO_CACHE=${NO_CACHE:-false}
    PARALLEL=${PARALLEL:-false}
    MULTI_ARCH=${MULTI_ARCH:-false}
    VERBOSE=${VERBOSE:-false}
    
    # 解析命令行参数
    local services_args=()
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -r|--registry)
                REGISTRY="$2"
                shift 2
                ;;
            -p|--repository)
                REPOSITORY="$2"
                shift 2
                ;;
            -t|--tag)
                TAG="$2"
                shift 2
                ;;
            -a|--platform)
                PLATFORM="$2"
                shift 2
                ;;
            --multi-arch)
                MULTI_ARCH=true
                PLATFORM="linux/amd64,linux/arm64"
                shift
                ;;
            --push)
                PUSH=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --parallel)
                PARALLEL=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                services_args+=("$1")
                shift
                ;;
        esac
    done
    
    # 获取服务列表
    local services=($(get_services "${services_args[@]}"))
    
    if [ ${#services[@]} -eq 0 ]; then
        log_error "没有找到要构建的服务"
        exit 1
    fi
    
    # 显示构建摘要
    show_summary "${services[@]}"
    
    # 检查Docker Buildx
    if [ "$MULTI_ARCH" = "true" ] || [ "$PUSH" = "true" ]; then
        check_buildx
    fi
    
    # 执行构建
    local start_time=$(date +%s)
    
    if [ "$PARALLEL" = "true" ] && [ ${#services[@]} -gt 1 ]; then
        build_parallel "${services[@]}"
    else
        build_sequential "${services[@]}"
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "构建完成，耗时: ${duration}秒"
    
    # 清理缓存
    if [ "$NO_CACHE" = "true" ]; then
        cleanup_cache
    fi
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
