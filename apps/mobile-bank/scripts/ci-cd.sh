#!/bin/bash

# Mobile Bank CI/CD Pipeline 脚本
# 适用于Jenkins、GitLab CI、GitHub Actions等CI/CD系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 环境变量
PROJECT_NAME=${PROJECT_NAME:-"mobile-bank"}
BUILD_NUMBER=${BUILD_NUMBER:-$(date +%Y%m%d%H%M%S)}
GIT_COMMIT=${GIT_COMMIT:-$(git rev-parse --short HEAD)}
GIT_BRANCH=${GIT_BRANCH:-$(git rev-parse --abbrev-ref HEAD)}
ENVIRONMENT=${ENVIRONMENT:-"dev"}

# AWS配置
AWS_REGION=${AWS_REGION:-"ap-southeast-1"}
ECR_REGISTRY=${ECR_REGISTRY:-"************.dkr.ecr.ap-southeast-1.amazonaws.com"}
ECR_REPOSITORY=${ECR_REPOSITORY:-"onebank"}

# Kubernetes配置
K8S_NAMESPACE=${K8S_NAMESPACE:-"onebank"}
HELM_RELEASE_NAME=${HELM_RELEASE_NAME:-"mobile-bank"}

# 镜像标签策略
if [ "$GIT_BRANCH" = "main" ] || [ "$GIT_BRANCH" = "master" ]; then
    IMAGE_TAG="v${BUILD_NUMBER}-${GIT_COMMIT}"
elif [ "$GIT_BRANCH" = "develop" ]; then
    IMAGE_TAG="dev-${BUILD_NUMBER}-${GIT_COMMIT}"
else
    IMAGE_TAG="feature-${GIT_BRANCH}-${BUILD_NUMBER}-${GIT_COMMIT}"
fi

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 阶段1: 环境准备
stage_prepare() {
    log_info "=== 阶段1: 环境准备 ==="
    
    # 检查必要的环境变量
    local required_vars=("AWS_ACCESS_KEY_ID" "AWS_SECRET_ACCESS_KEY" "KUBECONFIG")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "环境变量 $var 未设置"
            exit 1
        fi
    done
    
    # 安装依赖工具
    if ! command -v helm &> /dev/null; then
        log_info "安装Helm..."
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_info "安装kubectl..."
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
    fi
    
    # 配置AWS CLI
    aws configure set region $AWS_REGION
    
    log_success "环境准备完成"
}

# 阶段2: 代码质量检查
stage_quality_check() {
    log_info "=== 阶段2: 代码质量检查 ==="
    
    # Helm Chart语法检查
    log_info "检查Helm Chart语法..."
    helm lint .
    
    # Kubernetes资源验证
    log_info "验证Kubernetes资源..."
    helm template . --debug --dry-run > /tmp/k8s-resources.yaml
    kubectl apply --dry-run=client -f /tmp/k8s-resources.yaml
    
    # 安全扫描 (可选)
    if command -v trivy &> /dev/null; then
        log_info "执行安全扫描..."
        trivy fs .
    fi
    
    log_success "代码质量检查通过"
}

# 阶段3: 构建镜像
stage_build() {
    log_info "=== 阶段3: 构建Docker镜像 ==="
    
    # 登录ECR
    log_info "登录AWS ECR..."
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
    
    # 获取服务列表
    local services=$(find charts -maxdepth 1 -type d -exec basename {} \; | grep -v "^charts$")
    
    for service in $services; do
        if [ -f "charts/$service/Dockerfile" ]; then
            log_info "构建服务: $service"
            
            local image_name="$ECR_REGISTRY/$ECR_REPOSITORY/$service:$IMAGE_TAG"
            
            # 构建镜像
            docker build -t $image_name charts/$service/
            
            # 推送镜像
            docker push $image_name
            
            log_success "服务 $service 镜像构建并推送成功"
        else
            log_warning "服务 $service 没有Dockerfile，跳过构建"
        fi
    done
}

# 阶段4: 部署到开发环境
stage_deploy_dev() {
    log_info "=== 阶段4: 部署到开发环境 ==="
    
    if [ "$ENVIRONMENT" != "dev" ]; then
        log_info "跳过开发环境部署"
        return
    fi
    
    deploy_to_environment "dev" "onebank-dev"
}

# 阶段5: 运行测试
stage_test() {
    log_info "=== 阶段5: 运行测试 ==="
    
    if [ "$ENVIRONMENT" != "dev" ]; then
        log_info "跳过测试阶段"
        return
    fi
    
    # 等待部署完成
    log_info "等待Pod就绪..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=$HELM_RELEASE_NAME -n onebank-dev --timeout=300s
    
    # 健康检查
    log_info "执行健康检查..."
    local services=$(kubectl get svc -n onebank-dev -l app.kubernetes.io/instance=$HELM_RELEASE_NAME -o jsonpath='{.items[*].metadata.name}')
    
    for service in $services; do
        local port=$(kubectl get svc $service -n onebank-dev -o jsonpath='{.spec.ports[0].port}')
        kubectl run test-pod --image=curlimages/curl --rm -i --restart=Never -- \
            curl -f http://$service.onebank-dev.svc.cluster.local:$port/actuator/health
    done
    
    log_success "测试通过"
}

# 阶段6: 部署到测试环境
stage_deploy_test() {
    log_info "=== 阶段6: 部署到测试环境 ==="
    
    if [ "$GIT_BRANCH" != "develop" ] && [ "$GIT_BRANCH" != "main" ]; then
        log_info "跳过测试环境部署"
        return
    fi
    
    deploy_to_environment "test" "onebank-test"
}

# 阶段7: 部署到生产环境
stage_deploy_prod() {
    log_info "=== 阶段7: 部署到生产环境 ==="
    
    if [ "$GIT_BRANCH" != "main" ] && [ "$GIT_BRANCH" != "master" ]; then
        log_info "跳过生产环境部署"
        return
    fi
    
    # 生产环境需要手动确认
    if [ "$AUTO_DEPLOY_PROD" != "true" ]; then
        log_warning "生产环境部署需要手动确认"
        return
    fi
    
    deploy_to_environment "prod" "onebank"
}

# 通用部署函数
deploy_to_environment() {
    local env=$1
    local namespace=$2
    
    log_info "部署到 $env 环境 (命名空间: $namespace)"
    
    # 创建命名空间
    kubectl create namespace $namespace --dry-run=client -o yaml | kubectl apply -f -
    
    # 更新Helm依赖
    helm dependency update .
    
    # 部署应用
    helm upgrade --install $HELM_RELEASE_NAME . \
        --namespace $namespace \
        --set global.image.tag=$IMAGE_TAG \
        --set global.environment=$env \
        --values values-$env.yaml \
        --timeout 10m \
        --wait
    
    log_success "部署到 $env 环境成功"
}

# 阶段8: 清理
stage_cleanup() {
    log_info "=== 阶段8: 清理 ==="
    
    # 清理临时文件
    rm -f /tmp/k8s-resources.yaml
    
    # 清理Docker镜像 (保留最新的)
    docker image prune -f
    
    log_success "清理完成"
}

# 错误处理
handle_error() {
    log_error "Pipeline执行失败，正在清理..."
    stage_cleanup
    exit 1
}

# 设置错误处理
trap handle_error ERR

# 主函数
main() {
    log_info "开始Mobile Bank CI/CD Pipeline"
    log_info "项目: $PROJECT_NAME"
    log_info "分支: $GIT_BRANCH"
    log_info "提交: $GIT_COMMIT"
    log_info "构建号: $BUILD_NUMBER"
    log_info "镜像标签: $IMAGE_TAG"
    log_info "环境: $ENVIRONMENT"
    
    # 执行各个阶段
    stage_prepare
    stage_quality_check
    stage_build
    stage_deploy_dev
    stage_test
    stage_deploy_test
    stage_deploy_prod
    stage_cleanup
    
    log_success "Pipeline执行完成"
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
