#!/bin/bash

# Mobile Bank 健康检查脚本
# 用于验证部署后的服务健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认配置
DEFAULT_NAMESPACE="onebank"
DEFAULT_TIMEOUT=300
DEFAULT_INTERVAL=10

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 帮助信息
show_help() {
    cat << EOF
Mobile Bank 健康检查脚本

用法: $0 [选项] [服务名...]

选项:
    -n, --namespace NAMESPACE   Kubernetes命名空间 (默认: $DEFAULT_NAMESPACE)
    -t, --timeout TIMEOUT      超时时间(秒) (默认: $DEFAULT_TIMEOUT)
    -i, --interval INTERVAL     检查间隔(秒) (默认: $DEFAULT_INTERVAL)
    --wait-ready               等待Pod就绪
    --check-endpoints          检查Service端点
    --check-ingress            检查Ingress状态
    --full-check               执行完整健康检查
    -v, --verbose              详细输出
    -h, --help                 显示帮助信息

示例:
    $0 --full-check                        # 完整健康检查
    $0 fpaas-apigw-http --wait-ready       # 等待特定服务就绪
    $0 --check-endpoints -n onebank-dev    # 检查开发环境端点

EOF
}

# 获取服务列表
get_services() {
    if [ $# -gt 0 ]; then
        echo "$@"
    else
        kubectl get deployments -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | sort
    fi
}

# 等待Pod就绪
wait_pods_ready() {
    local services=("$@")
    
    log_info "等待Pod就绪..."
    
    for service in "${services[@]}"; do
        log_info "等待服务 $service 的Pod就绪..."
        
        if kubectl wait --for=condition=ready pod \
            -l app.kubernetes.io/name="$service" \
            -n "$NAMESPACE" \
            --timeout="${TIMEOUT}s" > /dev/null 2>&1; then
            log_success "服务 $service 的Pod已就绪"
        else
            log_error "服务 $service 的Pod未能在${TIMEOUT}秒内就绪"
            return 1
        fi
    done
    
    return 0
}

# 检查Pod状态
check_pod_status() {
    local services=("$@")
    local failed_services=()
    
    log_info "检查Pod状态..."
    
    for service in "${services[@]}"; do
        local pods=$(kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name="$service" -o jsonpath='{.items[*].metadata.name}')
        
        if [ -z "$pods" ]; then
            log_warning "服务 $service 没有找到Pod"
            failed_services+=("$service")
            continue
        fi
        
        local all_ready=true
        for pod in $pods; do
            local status=$(kubectl get pod "$pod" -n "$NAMESPACE" -o jsonpath='{.status.phase}')
            local ready=$(kubectl get pod "$pod" -n "$NAMESPACE" -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}')
            
            if [ "$status" != "Running" ] || [ "$ready" != "True" ]; then
                log_error "Pod $pod 状态异常: $status, Ready: $ready"
                all_ready=false
            fi
        done
        
        if [ "$all_ready" = true ]; then
            log_success "服务 $service 的所有Pod状态正常"
        else
            failed_services+=("$service")
        fi
    done
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        return 0
    else
        log_error "以下服务Pod状态异常: ${failed_services[*]}"
        return 1
    fi
}

# 检查Service端点
check_service_endpoints() {
    local services=("$@")
    local failed_services=()
    
    log_info "检查Service端点..."
    
    for service in "${services[@]}"; do
        local svc_name=$(kubectl get svc -n "$NAMESPACE" -l app.kubernetes.io/name="$service" -o jsonpath='{.items[0].metadata.name}')
        
        if [ -z "$svc_name" ]; then
            log_warning "服务 $service 没有找到Service"
            failed_services+=("$service")
            continue
        fi
        
        local endpoints=$(kubectl get endpoints "$svc_name" -n "$NAMESPACE" -o jsonpath='{.subsets[*].addresses[*].ip}')
        
        if [ -z "$endpoints" ]; then
            log_error "Service $svc_name 没有可用端点"
            failed_services+=("$service")
        else
            local endpoint_count=$(echo "$endpoints" | wc -w)
            log_success "Service $svc_name 有 $endpoint_count 个可用端点"
        fi
    done
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        return 0
    else
        log_error "以下服务端点异常: ${failed_services[*]}"
        return 1
    fi
}

# 检查健康端点
check_health_endpoints() {
    local services=("$@")
    local failed_services=()
    
    log_info "检查应用健康端点..."
    
    for service in "${services[@]}"; do
        local svc_name=$(kubectl get svc -n "$NAMESPACE" -l app.kubernetes.io/name="$service" -o jsonpath='{.items[0].metadata.name}')
        local port=$(kubectl get svc "$svc_name" -n "$NAMESPACE" -o jsonpath='{.spec.ports[0].port}')
        
        if [ -z "$svc_name" ] || [ -z "$port" ]; then
            log_warning "服务 $service 信息不完整，跳过健康检查"
            continue
        fi
        
        log_info "检查服务 $service 的健康端点..."
        
        # 使用临时Pod进行健康检查
        local health_check_result=$(kubectl run health-check-$service --image=curlimages/curl --rm -i --restart=Never --timeout=30s -- \
            curl -s -f "http://$svc_name.$NAMESPACE.svc.cluster.local:$port/actuator/health" 2>/dev/null || echo "FAILED")
        
        if [[ "$health_check_result" == *"UP"* ]]; then
            log_success "服务 $service 健康检查通过"
        else
            log_error "服务 $service 健康检查失败"
            failed_services+=("$service")
        fi
    done
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        return 0
    else
        log_error "以下服务健康检查失败: ${failed_services[*]}"
        return 1
    fi
}

# 检查Ingress状态
check_ingress_status() {
    log_info "检查Ingress状态..."
    
    local ingresses=$(kubectl get ingress -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}')
    
    if [ -z "$ingresses" ]; then
        log_warning "没有找到Ingress资源"
        return 0
    fi
    
    local failed_ingresses=()
    
    for ingress in $ingresses; do
        local address=$(kubectl get ingress "$ingress" -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
        
        if [ -z "$address" ]; then
            log_error "Ingress $ingress 没有分配地址"
            failed_ingresses+=("$ingress")
        else
            log_success "Ingress $ingress 地址: $address"
        fi
    done
    
    if [ ${#failed_ingresses[@]} -eq 0 ]; then
        return 0
    else
        log_error "以下Ingress状态异常: ${failed_ingresses[*]}"
        return 1
    fi
}

# 生成健康报告
generate_health_report() {
    local services=("$@")
    local report_file="health-report-$(date +%Y%m%d-%H%M%S).txt"
    
    log_info "生成健康报告: $report_file"
    
    {
        echo "=== Mobile Bank 健康检查报告 ==="
        echo "生成时间: $(date)"
        echo "命名空间: $NAMESPACE"
        echo "检查的服务: ${services[*]}"
        echo ""
        
        echo "=== Pod 状态 ==="
        kubectl get pods -n "$NAMESPACE" -o wide
        echo ""
        
        echo "=== Service 状态 ==="
        kubectl get svc -n "$NAMESPACE"
        echo ""
        
        echo "=== Ingress 状态 ==="
        kubectl get ingress -n "$NAMESPACE"
        echo ""
        
        echo "=== 资源使用情况 ==="
        kubectl top pods -n "$NAMESPACE" 2>/dev/null || echo "Metrics server不可用"
        echo ""
        
        echo "=== 事件信息 ==="
        kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' | tail -20
        
    } > "$report_file"
    
    log_success "健康报告已生成: $report_file"
}

# 完整健康检查
full_health_check() {
    local services=("$@")
    local checks_passed=0
    local total_checks=4
    
    log_info "开始完整健康检查..."
    
    # 检查1: Pod状态
    if check_pod_status "${services[@]}"; then
        ((checks_passed++))
    fi
    
    # 检查2: Service端点
    if check_service_endpoints "${services[@]}"; then
        ((checks_passed++))
    fi
    
    # 检查3: 健康端点
    if check_health_endpoints "${services[@]}"; then
        ((checks_passed++))
    fi
    
    # 检查4: Ingress状态
    if check_ingress_status; then
        ((checks_passed++))
    fi
    
    # 生成报告
    generate_health_report "${services[@]}"
    
    # 总结
    log_info "健康检查完成: $checks_passed/$total_checks 项检查通过"
    
    if [ $checks_passed -eq $total_checks ]; then
        log_success "所有健康检查通过"
        return 0
    else
        log_error "部分健康检查失败"
        return 1
    fi
}

# 主函数
main() {
    # 设置默认值
    NAMESPACE=${NAMESPACE:-$DEFAULT_NAMESPACE}
    TIMEOUT=${TIMEOUT:-$DEFAULT_TIMEOUT}
    INTERVAL=${INTERVAL:-$DEFAULT_INTERVAL}
    WAIT_READY=false
    CHECK_ENDPOINTS=false
    CHECK_INGRESS=false
    FULL_CHECK=false
    VERBOSE=false
    
    # 解析命令行参数
    local services_args=()
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -i|--interval)
                INTERVAL="$2"
                shift 2
                ;;
            --wait-ready)
                WAIT_READY=true
                shift
                ;;
            --check-endpoints)
                CHECK_ENDPOINTS=true
                shift
                ;;
            --check-ingress)
                CHECK_INGRESS=true
                shift
                ;;
            --full-check)
                FULL_CHECK=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                set -x
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                services_args+=("$1")
                shift
                ;;
        esac
    done
    
    # 获取服务列表
    local services=($(get_services "${services_args[@]}"))
    
    if [ ${#services[@]} -eq 0 ]; then
        log_error "没有找到要检查的服务"
        exit 1
    fi
    
    log_info "开始健康检查，命名空间: $NAMESPACE"
    log_info "检查的服务: ${services[*]}"
    
    # 执行相应的检查
    local exit_code=0
    
    if [ "$FULL_CHECK" = true ]; then
        full_health_check "${services[@]}" || exit_code=1
    else
        if [ "$WAIT_READY" = true ]; then
            wait_pods_ready "${services[@]}" || exit_code=1
        fi
        
        if [ "$CHECK_ENDPOINTS" = true ]; then
            check_service_endpoints "${services[@]}" || exit_code=1
        fi
        
        if [ "$CHECK_INGRESS" = true ]; then
            check_ingress_status || exit_code=1
        fi
        
        # 如果没有指定特定检查，执行基本检查
        if [ "$WAIT_READY" = false ] && [ "$CHECK_ENDPOINTS" = false ] && [ "$CHECK_INGRESS" = false ]; then
            check_pod_status "${services[@]}" || exit_code=1
        fi
    fi
    
    exit $exit_code
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
