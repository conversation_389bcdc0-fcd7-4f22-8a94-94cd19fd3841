# Mobile Bank Makefile
# 提供便捷的构建和部署命令

# 变量定义
PROJECT_NAME := mobile-bank
CHART_NAME := mobile-bank
DEFAULT_NAMESPACE := onebank
DEFAULT_TAG := latest
DEFAULT_ENV := dev

# AWS配置
AWS_REGION := ap-southeast-1
ECR_REGISTRY := ************.dkr.ecr.ap-southeast-1.amazonaws.com
ECR_REPOSITORY := onebank

# 获取Git信息
GIT_COMMIT := $(shell git rev-parse --short HEAD)
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
BUILD_TIME := $(shell date +%Y%m%d%H%M%S)

# 动态标签
ifeq ($(GIT_BRANCH),main)
    IMAGE_TAG := v$(BUILD_TIME)-$(GIT_COMMIT)
else ifeq ($(GIT_BRANCH),develop)
    IMAGE_TAG := dev-$(BUILD_TIME)-$(GIT_COMMIT)
else
    IMAGE_TAG := feature-$(GIT_BRANCH)-$(BUILD_TIME)-$(GIT_COMMIT)
endif

# 颜色定义
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m

# 默认目标
.DEFAULT_GOAL := help

# 帮助信息
.PHONY: help
help: ## 显示帮助信息
	@echo "Mobile Bank 构建和部署工具"
	@echo ""
	@echo "用法: make [目标] [变量=值]"
	@echo ""
	@echo "目标:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "变量:"
	@echo "  $(BLUE)NAMESPACE$(NC)     Kubernetes命名空间 (默认: $(DEFAULT_NAMESPACE))"
	@echo "  $(BLUE)TAG$(NC)           镜像标签 (默认: $(DEFAULT_TAG))"
	@echo "  $(BLUE)ENV$(NC)           部署环境 (dev/test/prod) (默认: $(DEFAULT_ENV))"
	@echo "  $(BLUE)SERVICE$(NC)       指定服务名称"
	@echo ""
	@echo "示例:"
	@echo "  make build TAG=v1.0.0"
	@echo "  make deploy ENV=prod NAMESPACE=onebank"
	@echo "  make logs SERVICE=fpaas-apigw-http"

# 检查依赖
.PHONY: check-deps
check-deps: ## 检查依赖工具
	@echo "$(BLUE)[INFO]$(NC) 检查依赖工具..."
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)[ERROR]$(NC) Docker未安装"; exit 1; }
	@command -v kubectl >/dev/null 2>&1 || { echo "$(RED)[ERROR]$(NC) kubectl未安装"; exit 1; }
	@command -v helm >/dev/null 2>&1 || { echo "$(RED)[ERROR]$(NC) Helm未安装"; exit 1; }
	@command -v aws >/dev/null 2>&1 || { echo "$(RED)[ERROR]$(NC) AWS CLI未安装"; exit 1; }
	@echo "$(GREEN)[SUCCESS]$(NC) 所有依赖工具检查通过"

# ECR登录
.PHONY: ecr-login
ecr-login: check-deps ## 登录AWS ECR
	@echo "$(BLUE)[INFO]$(NC) 登录AWS ECR..."
	@aws ecr get-login-password --region $(AWS_REGION) | docker login --username AWS --password-stdin $(ECR_REGISTRY)
	@echo "$(GREEN)[SUCCESS]$(NC) ECR登录成功"

# 构建镜像
.PHONY: build
build: ecr-login ## 构建Docker镜像
	@echo "$(BLUE)[INFO]$(NC) 构建Docker镜像..."
	@./deploy.sh build -t $(or $(TAG),$(IMAGE_TAG))
	@echo "$(GREEN)[SUCCESS]$(NC) 镜像构建完成"

# 推送镜像
.PHONY: push
push: ## 推送镜像到ECR
	@echo "$(BLUE)[INFO]$(NC) 推送镜像到ECR..."
	@./deploy.sh push -t $(or $(TAG),$(IMAGE_TAG))
	@echo "$(GREEN)[SUCCESS]$(NC) 镜像推送完成"

# 构建并推送
.PHONY: build-push
build-push: build push ## 构建并推送镜像

# Helm依赖更新
.PHONY: helm-deps
helm-deps: check-deps ## 更新Helm依赖
	@echo "$(BLUE)[INFO]$(NC) 更新Helm依赖..."
	@helm dependency update .
	@echo "$(GREEN)[SUCCESS]$(NC) Helm依赖更新完成"

# Helm语法检查
.PHONY: helm-lint
helm-lint: check-deps ## 检查Helm Chart语法
	@echo "$(BLUE)[INFO]$(NC) 检查Helm Chart语法..."
	@helm lint .
	@echo "$(GREEN)[SUCCESS]$(NC) Helm Chart语法检查通过"

# 模板渲染测试
.PHONY: helm-template
helm-template: helm-deps ## 渲染Helm模板
	@echo "$(BLUE)[INFO]$(NC) 渲染Helm模板..."
	@helm template $(CHART_NAME) . \
		--values values-$(or $(ENV),$(DEFAULT_ENV)).yaml \
		--set global.image.tag=$(or $(TAG),$(IMAGE_TAG)) \
		--debug

# 部署到开发环境
.PHONY: deploy-dev
deploy-dev: helm-deps ## 部署到开发环境
	@echo "$(BLUE)[INFO]$(NC) 部署到开发环境..."
	@./deploy.sh deploy -n onebank-dev -e dev -t $(or $(TAG),$(IMAGE_TAG))
	@echo "$(GREEN)[SUCCESS]$(NC) 开发环境部署完成"

# 部署到测试环境
.PHONY: deploy-test
deploy-test: helm-deps ## 部署到测试环境
	@echo "$(BLUE)[INFO]$(NC) 部署到测试环境..."
	@./deploy.sh deploy -n onebank-test -e test -t $(or $(TAG),$(IMAGE_TAG))
	@echo "$(GREEN)[SUCCESS]$(NC) 测试环境部署完成"

# 部署到生产环境
.PHONY: deploy-prod
deploy-prod: helm-deps ## 部署到生产环境
	@echo "$(YELLOW)[WARNING]$(NC) 即将部署到生产环境，请确认..."
	@read -p "确认部署到生产环境? (y/N): " confirm && [ "$$confirm" = "y" ]
	@./deploy.sh deploy -n onebank -e prod -t $(or $(TAG),$(IMAGE_TAG))
	@echo "$(GREEN)[SUCCESS]$(NC) 生产环境部署完成"

# 通用部署
.PHONY: deploy
deploy: helm-deps ## 部署到指定环境
	@./deploy.sh deploy -n $(or $(NAMESPACE),$(DEFAULT_NAMESPACE)) -e $(or $(ENV),$(DEFAULT_ENV)) -t $(or $(TAG),$(IMAGE_TAG))

# 升级部署
.PHONY: upgrade
upgrade: ## 升级现有部署
	@./deploy.sh upgrade -n $(or $(NAMESPACE),$(DEFAULT_NAMESPACE)) -e $(or $(ENV),$(DEFAULT_ENV)) -t $(or $(TAG),$(IMAGE_TAG))

# 回滚部署
.PHONY: rollback
rollback: check-deps ## 回滚部署
	@echo "$(YELLOW)[WARNING]$(NC) 即将回滚部署..."
	@./deploy.sh rollback -n $(or $(NAMESPACE),$(DEFAULT_NAMESPACE))

# 查看状态
.PHONY: status
status: check-deps ## 查看部署状态
	@./deploy.sh status -n $(or $(NAMESPACE),$(DEFAULT_NAMESPACE))

# 查看日志
.PHONY: logs
logs: check-deps ## 查看服务日志
	@./deploy.sh logs -n $(or $(NAMESPACE),$(DEFAULT_NAMESPACE)) $(if $(SERVICE),-s $(SERVICE))

# 端口转发
.PHONY: port-forward
port-forward: check-deps ## 端口转发到本地
	@echo "$(BLUE)[INFO]$(NC) 设置端口转发..."
	@kubectl port-forward -n $(or $(NAMESPACE),$(DEFAULT_NAMESPACE)) svc/$(or $(SERVICE),fpaas-apigw-http) 8080:8084

# 清理资源
.PHONY: clean
clean: check-deps ## 清理部署资源
	@echo "$(YELLOW)[WARNING]$(NC) 即将清理所有资源..."
	@./deploy.sh clean -n $(or $(NAMESPACE),$(DEFAULT_NAMESPACE))

# 运行测试
.PHONY: test
test: check-deps ## 运行健康检查测试
	@echo "$(BLUE)[INFO]$(NC) 运行健康检查测试..."
	@kubectl run test-pod --image=curlimages/curl --rm -i --restart=Never -- \
		curl -f http://$(or $(SERVICE),fpaas-apigw-http).$(or $(NAMESPACE),$(DEFAULT_NAMESPACE)).svc.cluster.local:8084/actuator/health

# 完整的CI/CD流程
.PHONY: ci-cd
ci-cd: helm-lint build-push deploy-dev test ## 执行完整的CI/CD流程

# 开发者快速启动
.PHONY: dev-start
dev-start: build-push deploy-dev port-forward ## 开发者快速启动（构建、部署、端口转发）

# 清理Docker镜像
.PHONY: docker-clean
docker-clean: ## 清理本地Docker镜像
	@echo "$(BLUE)[INFO]$(NC) 清理Docker镜像..."
	@docker image prune -f
	@docker system prune -f
	@echo "$(GREEN)[SUCCESS]$(NC) Docker镜像清理完成"

# 显示版本信息
.PHONY: version
version: ## 显示版本信息
	@echo "项目: $(PROJECT_NAME)"
	@echo "Git分支: $(GIT_BRANCH)"
	@echo "Git提交: $(GIT_COMMIT)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "镜像标签: $(IMAGE_TAG)"

# 生成部署报告
.PHONY: report
report: check-deps ## 生成部署报告
	@echo "$(BLUE)[INFO]$(NC) 生成部署报告..."
	@echo "=== Mobile Bank 部署报告 ===" > deployment-report.txt
	@echo "生成时间: $(shell date)" >> deployment-report.txt
	@echo "Git分支: $(GIT_BRANCH)" >> deployment-report.txt
	@echo "Git提交: $(GIT_COMMIT)" >> deployment-report.txt
	@echo "镜像标签: $(IMAGE_TAG)" >> deployment-report.txt
	@echo "" >> deployment-report.txt
	@echo "=== Helm Release 状态 ===" >> deployment-report.txt
	@helm status $(CHART_NAME) -n $(or $(NAMESPACE),$(DEFAULT_NAMESPACE)) >> deployment-report.txt 2>&1 || true
	@echo "" >> deployment-report.txt
	@echo "=== Pod 状态 ===" >> deployment-report.txt
	@kubectl get pods -n $(or $(NAMESPACE),$(DEFAULT_NAMESPACE)) -l app.kubernetes.io/instance=$(CHART_NAME) >> deployment-report.txt 2>&1 || true
	@echo "$(GREEN)[SUCCESS]$(NC) 部署报告已生成: deployment-report.txt"
