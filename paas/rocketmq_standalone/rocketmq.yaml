# templates/nameserver-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rocketmq-nameserver
  namespace: paas
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rocketmq
      component: nameserver
  template:
    metadata:
      labels:
        app: rocketmq
        component: nameserver
    spec:
      containers:
        - name: nameserver
          image: apache/rocketmq:5.1.4
          imagePullPolicy: IfNotPresent
          command: ["/bin/bash", "-c"]
          args: ["exec /home/<USER>/rocketmq-5.1.4/bin/mqnamesrv"]
          ports:
            - containerPort: 9876
---
# templates/broker-statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rocketmq-broker
  namespace: paas
spec:
  serviceName: rocketmq-broker
  replicas: 1
  selector:
    matchLabels:
      app: rocketmq
      component: broker
  template:
    metadata:
      labels:
        app: rocketmq
        component: broker
    spec:
      containers:
        - name: broker
          image: apache/rocketmq:5.1.4
          imagePullPolicy: IfNotPresent
          command: ["/bin/bash", "-c"]
          args:
            - |
              echo "brokerClusterName=DevCluster" > /tmp/broker.conf
              echo "brokerName=broker-$(hostname)" >> /tmp/broker.conf
              echo "brokerId=0" >> /tmp/broker.conf
              echo "namesrvAddr=rocketmq-nameserver:9876" >> /tmp/broker.conf
              exec /home/<USER>/rocketmq-5.1.4/bin/mqbroker -n rocketmq-nameserver:9876 -c /tmp/broker.conf
          ports:
            - containerPort: 10911
---
# templates/service-nameserver.yaml
apiVersion: v1
kind: Service
metadata:
  name: rocketmq-nameserver
  namespace: paas
spec:
  selector:
    app: rocketmq
    component: nameserver
  ports:
    - port: 9876
      targetPort: 9876
---
# templates/service-broker.yaml
apiVersion: v1
kind: Service
metadata:
  name: rocketmq-broker
  namespace: paas
spec:
  clusterIP: None
  selector:
    app: rocketmq
    component: broker
  ports:
    - port: 10911
      targetPort: 10911
