---
apiVersion: v1
kind: Service
metadata:
  name: influxdb
  namespace: paas
  labels:
    app: influxdb
spec:
  ports:
    - port: 8086
      targetPort: 8086
      name: http
  selector:
    app: influxdb
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: influxdb
  namespace: paas
spec:
  replicas: 1
  selector:
    matchLabels:
      app: influxdb
  template:
    metadata:
      labels:
        app: influxdb
    spec:
      containers:
        - name: influxdb
          image: influxdb:1.7.10
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8086
          env:
            - name: INFLUXDB_DB
              value: mydb              # 启动时自动创建数据库（可选）
            - name: INFLUXDB_HTTP_AUTH_ENABLED
              value: "false"           # 不启用认证（开发场景）
