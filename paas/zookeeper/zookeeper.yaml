---
apiVersion: v1
kind: Service
metadata:
  name: zookeeper
  namespace: paas
  labels:
    app: zookeeper
spec:
  ports:
    - port: 2181
      name: client
  selector:
    app: zookeeper
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zookeeper
  namespace: paas
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      containers:
        - name: zookeeper
          image: zookeeper:3.6.0
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 2181
          env:
            - name: ZOO_MY_ID
              value: "1"
            - name: ZOO_SERVERS
              value: server.1=0.0.0.0:2888:3888
