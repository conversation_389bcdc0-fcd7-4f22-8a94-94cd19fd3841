apiVersion: v1
kind: ConfigMap
metadata:
  name: nacosx-application-conf
  namespace: paas
data:
  application.properties: |
    #
    # Copyright 1999-2018 Alibaba Group Holding Ltd.
    #
    # Licensed under the Apache License, Version 2.0 (the "License");
    # you may not use this file except in compliance with the License.
    # You may obtain a copy of the License at
    #
    #      http://www.apache.org/licenses/LICENSE-2.0
    #
    # Unless required by applicable law or agreed to in writing, software
    # distributed under the License is distributed on an "AS IS" BASIS,
    # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    # See the License for the specific language governing permissions and
    # limitations under the License.
    #
    #*************** Spring Boot Related Configurations ***************#
    ### Default web context path:
    server.servlet.contextPath=/nacos
    ### Default web server port:
    server.port=8848
    server.error.include-message=ALWAYS
    servicename=nacos-x-server
    debugport=10848
    #*************** Network Related Configurations ***************#
    ### If prefer hostname over ip for Nacos server addresses in cluster.conf:
    # nacos.inetutils.prefer-hostname-over-ip=false
    ### Specify local server's IP:
    # nacos.inetutils.ip-address=

    spring.application.name=${servicename}
    ### Metrics for influx
    management.metrics.export.influx.enabled=false
    #management.metrics.export.influx.db=springboot
    #management.metrics.export.influx.uri=http://localhost:9086
    #management.metrics.export.influx.auto-create-db=true
    #management.metrics.export.influx.consistency=one
    #management.metrics.export.influx.compressed=true
    ### Metrics for elastic search
    management.metrics.export.elastic.enabled=false
    #management.metrics.export.elastic.host=http://localhost:9200
    #\u591A\u4E2D\u5FC3\u5C0F\u96C6\u7FA4\u7F13\u5B58\u5237\u65B0\u5F00\u5173
    config.zone.refresh.enable=false
    config.zone.refresh.cron=*/30 * * * * ?
    #default-namespace-id, \u5982\u679C\u4FEE\u6539\u6B64\u5904\uFF0C\u9700\u8981\u540C\u6B65\u4FEE\u6539\u51FA\u5382\u811A\u672C\u3002
    nacos.default-namespace-id=galaxy

    nacos.prometheus.metrics.enabled=false
    spring.security.enabled=false
    nacos.security.ignore.urls=/,/**/*.css,/**/*.js,/**/*.html,/**/*.map,/**/*.svg,/**/*.png,/**/*.ico,/console-ui/public/**,/v1/auth/login,/v1/console/health,/v1/cs/**,/v1/ns/**,/v1/cmdb/**,/actuator/**

    ### The auth system to use, currently only 'nacos' and 'ldap' is supported:
    nacos.core.auth.system.type=nacos

    ### If turn on auth system:
    nacos.core.auth.enabled=false
    ### Turn on/off caching of auth information. By turning on this switch, the update of auth information would have a 15 seconds delay.
    nacos.core.auth.caching.enabled=false

    ### Since 1.4.1, Turn on/off white auth for user-agent: nacos-server, only for upgrade from old version.
    nacos.core.auth.enable.userAgentAuthWhite=false

    ### Since 1.4.1, worked when nacos.core.auth.enabled=true and nacos.core.auth.enable.userAgentAuthWhite=false.
    ### The two properties is the white list for auth and used by identity the request from other server.
    nacos.core.auth.server.identity.key=appName
    nacos.core.auth.server.identity.value=aries

    ### worked when nacos.core.auth.system.type=nacos
    ### The token expiration in seconds:
    nacos.core.auth.plugin.nacos.token.cache.enable=false
    nacos.core.auth.plugin.nacos.token.expire.seconds=18000
    ### The default token (Base64 String):
    # SecretKey012345678901234567890123456789012345678901234567890123456789
    nacos.core.auth.plugin.nacos.token.secret.key=SecretKey012345678901234567890123456789012345678901234567890123456789

    ### log ###
    nacos.logs.path=/app/dcits/app-run/logs/nacos-x-server
    #*************** Config Module Related Configurations ***************#
    ### If use MySQL as datasource:
    ### Deprecated configuration property, it is recommended to use `spring.sql.init.platform` replaced.
    spring.datasource.platform=mysql
    ### Count of DB:
    db.num=1
    #*************** Database Configurations ***************#

    ### Connect URL of MYSQL:
    db.url.0=**********************************************************************************************************************************************************************************************************************
    db.user.0=root
    db.password.0=rootpasswd#45r
    db.pool.config.connectionTestQuery=select 1
    db.pool.config.driverClassName=com.mysql.cj.jdbc.Driver
    ### Connection pool configuration: hikariCP
    db.pool.config.connectionTimeout=30000
    db.pool.config.validationTimeout=10000
    db.pool.config.maximumPoolSize=20
    db.pool.config.minimumIdle=2