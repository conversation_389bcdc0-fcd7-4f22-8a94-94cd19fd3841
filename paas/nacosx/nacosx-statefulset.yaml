apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: nacosx
  namespace: paas
spec:
  serviceName: nacosx-headless
  replicas: 1
  selector:
    matchLabels:
      app: nacosx
  template:
    metadata:
      labels:
        app: nacosx
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: nacosx
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/nacos-x-server:0.0.1-alpha
          ports:
            - containerPort: 8848
              name: http
            - containerPort: 7848
              name: raft
            - containerPort: 9848
              name: grpc
            - containerPort: 9849
              name: grpc-metrics
          resources:
            limits:
              cpu: "2"
              memory: "2Gi"
            requests:
              cpu: "1"
              memory: "1Gi"
          env:
            - name: MODE
              value: "cluster"
            - name: NACOS_SERVERS
              value: "nacosx-0.nacosx-headless.paas.svc.cluster.local:8848"
            - name: PREFER_HOST_MODE
              value: "hostname"
            - name: NACOS_REPLICAS
              value: "1"
            - name: JVM_XMS
              value: "2g"
            - name: JVM_XMX
              value: "2g"
            - name: JVM_XMN
              value: "1g"
          volumeMounts:
            - name: cluster-conf  # 新增：挂载 cluster.conf
              mountPath: /app/dcits/app-run/galaxy/nacos-x-server/nacos-x-server/conf/cluster.conf
              subPath: cluster.conf  # 只挂载单个文件
            - name: application-conf  # 新增：挂载 application.properties
              mountPath: /app/dcits/app-run/galaxy/nacos-x-server/nacos-x-server/conf/application.properties
              subPath: application.properties  # 只挂载单个文件
          # livenessProbe:
          #   httpGet:
          #     path: /nacos/health
          #     port: 8848
          #   initialDelaySeconds: 30
          #   periodSeconds: 10
          # readinessProbe:
          #   httpGet:
          #     path: /nacos/health
          #     port: 8848
          #   initialDelaySeconds: 30
          #   periodSeconds: 10
      volumes:
        - name: cluster-conf     # 引用 ConfigMap
          configMap:
            name: nacosx-cluster-conf
            items:
              - key: cluster.conf
                path: cluster.conf
        - name: application-conf     # 引用 ConfigMap
          configMap:
            name: nacosx-application-conf
            items:
              - key: application.properties
                path: application.properties